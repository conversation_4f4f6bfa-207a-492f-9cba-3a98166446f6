import path from 'path';
import axios from 'axios';
import * as lambdaLocal from 'lambda-local';
import { KinesisStreamEvent, KinesisStreamRecord } from 'aws-lambda';
import {
  Operation,
  ChargepointData,
  DataType,
  PutMapDataUpsertRequest,
  SiteData,
  PutMapDataRequest,
  ChargepointDeleteRequest,
} from '../../shared/put-map-data-api.types';
import { Provider } from '../../shared/elastic-search.types';
import * as Utils from '../../shared/utils';
import * as ElasticSearch from './elastic-search';
import { encode } from './kinesis';
import {
  kEncodedValidData1,
  kEncodedValidData2,
  kValidRequest,
  kValidUpdateRequest,
} from './test-data';

jest.mock('./env');

jest.mock('axios');

jest.mock('winston', () => {
  const actualWinston = jest.requireActual('winston');
  return {
    ...actualWinston,
    createLogger: () => ({
      log: jest.fn(),
    }),
  };
});

jest.spyOn(global.console, 'log').mockImplementation(jest.fn());
jest.spyOn(global.console, 'debug').mockImplementation(jest.fn());
jest.spyOn(global.console, 'info').mockImplementation(jest.fn());
jest.spyOn(global.console, 'warn').mockImplementation(jest.fn());
jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

const searchSiteIdsInBatches = jest
  .spyOn(ElasticSearch, 'searchSiteIdsInBatches')
  .mockResolvedValue([]);

const searchChargepointsInBatches = jest
  .spyOn(ElasticSearch, 'searchChargepointsInBatches')
  .mockResolvedValue([]);

jest.spyOn(Utils, 'randomInt').mockReturnValue(0);

// const mockAxios = axios as jest.Mocked<typeof axios>;
// jest.mock('axios', () => {
//   const axiosMock = jest.createMockFromModule<typeof mockAxios>('axios');
//   axiosMock.create = jest.fn(() => axiosMock);

//   return axiosMock;
// });

// const mockedAxios = axios as jest.Mocked<typeof axios>;

const runLocal = (event: KinesisStreamEvent) =>
  lambdaLocal.execute({
    event,
    lambdaPath: path.join(__dirname, './index.ts'),
    lambdaHandler: 'handler',
    timeoutMs: 900000,
  });

afterEach(() => {
  jest.clearAllMocks();
});

describe('MapServiceStack/GetDataBPCM', () => {
  describe('handler()', () => {
    it('should process events without errors', async () => {
      const r1 = { kinesis: { data: kEncodedValidData1 } };
      const r2 = { kinesis: { data: kEncodedValidData2 } };
      const r3 = encode(kValidRequest);
      expect(r3).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [r1, r2, r3] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);
      expect(axios.post.mock.calls[0][1]).toHaveLength(Records.length);
    });

    it('should ignore preformatted records', async () => {
      const record = encode({ ...kValidRequest, preformatted: true });
      expect(record).not.toBeNull();

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(0);
    });

    it('should skip records without site id', async () => {
      const record = encode({
        ...kValidRequest,
        payload: {
          ...kValidRequest.payload,
          details: {
            ...kValidRequest.payload.details,
            site_id: undefined,
          },
        },
      });

      expect(record).not.toBeNull();

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(0);
    });

    it('should enforce upserts', async () => {
      const record = encode({ ...kValidRequest, operation: 'UPDATE' });
      expect(record).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);
      expect(axios.post.mock.calls[0][1]).toHaveLength(1);
      expect(axios.post.mock.calls[0][1][0].operation).toBe('CREATE');
    });

    it('should send SiteRequests when site does not exist', async () => {
      const record = encode(kValidRequest);
      expect(record).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);

      const body = axios.post.mock.calls[0][1] as Array<
        PutMapDataUpsertRequest<SiteData>
      >;
      expect(body).toHaveLength(1);

      const req = body[0];
      expect(req.type).toBe(DataType.SITE);
      expect(req.serial).toBe(String(kValidRequest.payload.details.site_id));
      expect(req.payload.chargepoints).toHaveLength(1);
      expect(req.payload.chargepoints[0].providerInternalId).toBe(
        kValidRequest.serial,
      );
    });

    it('should group chargepoints in SiteRequests when site does not exist', async () => {
      const record1 = encode(kValidRequest);
      expect(record1).not.toBeNull();

      const record2 = encode({ ...kValidRequest, serial: 'BPU_3001' });
      expect(record2).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record1, record2] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);
      const body = axios.post.mock.calls[0][1] as Array<
        PutMapDataUpsertRequest<SiteData>
      >;
      expect(body).toHaveLength(1);

      const req = body[0];
      expect(req.type).toBe(DataType.SITE);
      expect(req.serial).toBe(String(kValidRequest.payload.details.site_id));
      expect(req.payload.chargepoints).toHaveLength(2);
      expect(
        req.payload.chargepoints?.some(
          (c) => c.providerInternalId === 'BPU_3000',
        ),
      ).toBe(true);
      expect(
        req.payload.chargepoints?.some(
          (c) => c.providerInternalId === 'BPU_3001',
        ),
      ).toBe(true);
    });

    it('should not duplicate chargepoints in SiteRequests when site does not exist', async () => {
      const record = encode(kValidRequest);
      expect(record).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record, record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);
      const body = axios.post.mock.calls[0][1] as Array<
        PutMapDataUpsertRequest<SiteData>
      >;
      expect(body).toHaveLength(1);

      const req = body[0];
      expect(req.payload.chargepoints).toHaveLength(1);
    });

    it('should send SiteRequests and ChargepointRequests when site exists', async () => {
      const record = encode(kValidRequest);
      expect(record).not.toBeNull();

      const siteId = `${Provider.BPCM}-${kValidRequest.payload.details.site_id}`;
      searchSiteIdsInBatches.mockResolvedValueOnce([siteId]);

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);

      const body = axios.post.mock.calls[0][1];
      expect(body).toHaveLength(2);

      const siteReq = body[0] as PutMapDataUpsertRequest<SiteData>;
      expect(siteReq.type).toBe(DataType.SITE);
      expect(siteReq.serial).toBe(
        String(kValidRequest.payload.details.site_id),
      );
      expect(siteReq.payload.chargepoints).toBeUndefined();

      const chargepointReq =
        body[1] as PutMapDataUpsertRequest<ChargepointData>;
      expect(chargepointReq.type).toBe(DataType.CHARGEPOINT);
      expect(chargepointReq.serial).toBe(kValidRequest.serial);
    });

    it('should handle site_id changes on chargepoints', async () => {
      const record = encode(kValidUpdateRequest);
      expect(record).not.toBeNull();

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      searchChargepointsInBatches.mockResolvedValueOnce([
        {
          providerInternalId: kValidUpdateRequest.serial,
          site: {
            siteId: 'BPCM-5001',
            chargepoints: [
              { providerInternalId: kValidUpdateRequest.serial },
              { providerInternalId: 'BPU_5001' },
            ],
          },
        },
      ]);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);
      const body = axios.post.mock.calls[0][1] as Array<PutMapDataRequest>;
      expect(body).toHaveLength(2);

      const createReq = body[0] as PutMapDataUpsertRequest<SiteData>;
      expect(createReq.type).toBe(DataType.SITE);
      expect(createReq.serial).toBe(
        String(kValidUpdateRequest.payload.details.site_id),
      );
      expect(createReq.payload.chargepoints).toHaveLength(1);
      expect(createReq.payload.chargepoints![0].providerInternalId).toBe(
        kValidUpdateRequest.serial,
      );

      const deleteReq = body[1] as ChargepointDeleteRequest;
      expect(deleteReq.type).toBe(DataType.CHARGEPOINT);
      expect(deleteReq.operation).toBe(Operation.DELETE);
      expect(deleteReq.serial).toBe(kValidUpdateRequest.serial);
      expect(deleteReq.payload.siteId).toBe('BPCM-5001');
    });

    it('should delete site when there is no other chargepoints', async () => {
      const record = encode({
        ...kValidRequest,
        operation: 'DELETE',
      });

      expect(record).not.toBeNull();

      const siteId = `${Provider.BPCM}-${kValidRequest.payload.details.site_id}`;
      searchSiteIdsInBatches.mockResolvedValueOnce([siteId]);

      searchChargepointsInBatches.mockResolvedValueOnce([
        {
          providerInternalId: kValidRequest.serial,
          site: {
            siteId: `${Provider.BPCM}-3000`,
            chargepoints: [
              {
                providerInternalId: kValidRequest.serial,
              },
            ],
          },
        },
      ]);

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);

      const body = axios.post.mock.calls[0][1] as Array<
        PutMapDataUpsertRequest<ChargepointData>
      >;
      expect(body).toHaveLength(1);

      const req = body[0];
      expect(req.operation).toBe(Operation.DELETE);
      expect(req.type).toBe(DataType.SITE);
      expect(req.serial).toBe(String(kValidRequest.payload.details.site_id));
    });

    it('should delete only the chargepoint when there is more chargepoints on the site', async () => {
      const record = encode({
        ...kValidRequest,
        operation: 'DELETE',
      });

      expect(record).not.toBeNull();

      const siteId = `${Provider.BPCM}-${kValidRequest.payload.details.site_id}`;
      searchSiteIdsInBatches.mockResolvedValueOnce([siteId]);

      searchChargepointsInBatches.mockResolvedValueOnce([
        {
          providerInternalId: kValidRequest.serial,
          site: {
            siteId: `${Provider.BPCM}-sBPU_3000`,
            chargepoints: [
              {
                providerInternalId: kValidRequest.serial,
              },
              {
                providerInternalId: 'some-other-chargepoint',
              },
            ],
          },
        },
      ]);

      const response = { data: { failed: [] } };
      axios.post.mockResolvedValueOnce(response);

      const Records = [record] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(1);

      const body = axios.post.mock.calls[0][1] as Array<
        PutMapDataUpsertRequest<ChargepointData>
      >;
      expect(body).toHaveLength(1);

      const req = body[0];
      expect(req.operation).toBe(Operation.DELETE);
      expect(req.type).toBe(DataType.CHARGEPOINT);
      expect(req.serial).toBe(kValidRequest.serial);
    });

    it('should retry on conflict', async () => {
      const r1 = { kinesis: { data: kEncodedValidData1 } };
      const r2 = { kinesis: { data: kEncodedValidData2 } };
      const r3 = encode(kValidRequest);
      expect(r3).not.toBeNull();

      axios.post.mockImplementationOnce((_: string, data: Array<unknown>) => {
        const failed = data.slice(0, 2).map((item) => ({ item, status: 409 }));

        return { data: { failed } };
      });

      const Records = [r1, r2, r3] as Array<KinesisStreamRecord>;
      await runLocal({ Records });

      expect(axios.post).toHaveBeenCalledTimes(2);
      expect(axios.post.mock.calls[0][1]).toHaveLength(Records.length);
      expect(axios.post.mock.calls[1][1]).toHaveLength(2);
    });
  });
});
