import { DeactivateTagEvent, Tag } from '../types/types';
import { logger } from './logger';

export function validateString(input: string): string {
    if (typeof input !== 'string') {
        logger.warn(`Invalid input type for string: ${typeof input}`);
        return '';
    }
    return input;
}

export function validateTag(tag: Tag): boolean {
    return !!(
        tag &&
        typeof tag === 'object' &&
        tag.tagId &&
        tag.tagCardNumber &&
        tag.tagNotes &&
        tag.tagStatus
    );
}

export function validateDeactivateTagEvent(
    event: DeactivateTagEvent,
): DeactivateTagEvent {
    if (!event || typeof event !== 'object') {
        throw new Error('Invalid deactivation event provided');
    }
    const hasUserId =
        !!event.userId &&
        typeof event.userId === 'string' &&
        event.userId !== '';
    if (!hasUserId) {
        throw new Error('userId is required');
    }
    if (
        !event.country ||
        typeof event.country !== 'string' ||
        event.country === ''
    ) {
        throw new Error('country is required when userId is provided');
    }
    let validTags: Tag[] = [];
    if (Array.isArray(event.tagIds) && event.tagIds.length > 0) {
        validTags = event.tagIds.filter((tag, index) => {
            const isValid = validateTag(tag);
            if (!isValid) {
                logger.warn(
                    `Tag at index ${index} is missing required fields and will be ignored.`,
                );
            }
            return isValid;
        });
        if (validTags.length === 0) {
            throw new Error('All provided tags are missing required fields');
        }
    }
    return {
        ...event,
        tagIds: Array.isArray(event.tagIds) ? validTags : [],
    };
}

export function validateDeactivateTagEventArray(
    events: DeactivateTagEvent[],
): DeactivateTagEvent[] {
    if (!Array.isArray(events)) {
        throw new Error('Events must be an array');
    }
    if (events.length === 0) {
        throw new Error('At least one event must be provided');
    }
    return events.map((event, index) => {
        try {
            return validateDeactivateTagEvent(event);
        } catch (error) {
            throw new Error(`Invalid event at index ${index}: ${error}`);
        }
    });
}
