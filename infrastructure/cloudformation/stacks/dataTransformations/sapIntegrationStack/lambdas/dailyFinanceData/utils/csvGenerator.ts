import { stringify } from 'csv-stringify';
import { Readable } from 'node:stream';
import { promisify } from 'node:util';
import { gzip } from 'node:zlib';
import { PROVIDER } from '../common/enums';
import { GenericObject } from '../common/interfaces';
import {
  BPCMObjectMap,
  DCSObjectMap,
  HTBObjectMap,
} from './mapProviderData/mapProviderData';
import { writeToS3 } from './writeToS3/writeToS3';

const gzipPromise = promisify(gzip);

export const generateCSVBuffer = async (
  mappedData: GenericObject[],
): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    const csvData: any[] = [];
    const csvHeaders = Object.keys(mappedData[0]);
    // Initialize the stringifier
    const stringifier = stringify({
      delimiter: ';',
    });
    // Use the readable stream api to consume CSV data
    stringifier.on('readable', () => {
      let row;
      while ((row = stringifier.read()) !== null) {
        csvData.push(row);
      }
    });
    // Catch any error
    stringifier.on('error', (err: any) => {
      const errMessage = `Error encountered while processing CSV data: ${err.message}`;
      console.error(errMessage);
      reject(errMessage);
    });

    // Write records to the stream
    stringifier.write(csvHeaders); // Write csv headers

    mappedData.forEach((cdrItem) => {
      stringifier.write(Object.values(cdrItem));
    }); // Write each row of values

    // Close the writable stream
    stringifier.end();

    stringifier.on('finish', function () {
      resolve(csvData[0]);
    });
  });
};

export const createCSVStream = async (csvBuffer: Buffer) => {
  const gzippedBuffer = await gzipPromise(csvBuffer);

  const uploadStream = new Readable();
  uploadStream._read = () => {};
  uploadStream.push(gzippedBuffer);
  uploadStream.push(null);

  return { stream: uploadStream, length: gzippedBuffer.length };
};

export const mapDataByProvider = (provider: string, data: GenericObject[]) => {
  const mappedData = data.map((item) => {
    switch (provider) {
      case PROVIDER.DCS:
        return DCSObjectMap(item);
      case PROVIDER.HASTOBE:
        return HTBObjectMap(item);
      case PROVIDER.BPCM:
        return BPCMObjectMap(item);
      default:
        throw new Error('Invalid provider');
    }
  });
  return mappedData;
};

export const putCSVFile = async (
  provider: PROVIDER,
  data: GenericObject[],
  startDate: string,
  endDate: string,
) => {
  try {
    if (data && data.length > 0) {
      const mappedData = mapDataByProvider(provider, data);
      console.info('Writing data to CSV file... ', JSON.stringify(mappedData));

      const csvData = await generateCSVBuffer(mappedData);
      const gzData = await createCSVStream(csvData);

      await writeToS3(provider, csvData, gzData, startDate, endDate)
        .then(() =>
          console.info(`File written successfully to S3 for: ${provider}`),
        )
        .catch((e) => {
          console.error(
            `Error writing file to S3. Error code: ${e.code}, Error message: ${e.message}`,
          );
          throw new Error(
            `Error writing file to S3. Error code: ${e.code}, Error message: ${e.message}`,
          );
        });
    } else {
      console.info(`No file generated for ${provider} as no data was found`);
    }
  } catch (err: any) {
    console.error(
      `Error saving file to S3 for transactionIds: ${data?.map(
        (e) => e.payment_id,
      )}`,
      err,
    );
    throw err;
  }
};
