import { PROVIDER } from '../../common/enums';
import { constructFile<PERSON>ey } from './constructFileKey';

describe('constructFileKey', () => {
  it('should construct key correctly for normal date', () => {
    const startDate = '2023-03-14';
    const endDate = '2023-03-15';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.BPCM,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/BPCM/2023/03/15/EMSP_BPCM_INV_20230314.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/BPCM/upload_date=2023-03-15/EMSP_BPCM_INV_20230314.csv',
    );
  });

  it('should handle leap year correctly', () => {
    const startDate = '2024-02-28';
    const endDate = '2024-02-29';
    const { dataHub<PERSON>ey, emspKey } = constructFileKey(
      PROVIDER.DCS,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/DCS/2024/02/29/EMSP_DCS_INV_20240228.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/DCS/upload_date=2024-02-29/EMSP_DCS_INV_20240228.csv',
    );
  });

  it('should handle month-end transition correctly', () => {
    const startDate = '2023-04-29';
    const endDate = '2023-04-30';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.HASTOBE,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/HTB/2023/04/30/EMSP_HTB_INV_20230429.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/HTB/upload_date=2023-04-30/EMSP_HTB_INV_20230429.csv',
    );
  });

  it('should handle year-end transition correctly', () => {
    const startDate = '2022-12-30';
    const endDate = '2022-12-31';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.BPCM,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/BPCM/2022/12/31/EMSP_BPCM_INV_20221230.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/BPCM/upload_date=2022-12-31/EMSP_BPCM_INV_20221230.csv',
    );
  });

  it('should handle year transition correctly', () => {
    const startDate = '2022-12-31';
    const endDate = '2023-01-01';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.BPCM,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/BPCM/2023/01/01/EMSP_BPCM_INV_20221231.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/BPCM/upload_date=2023-01-01/EMSP_BPCM_INV_20221231.csv',
    );
  });

  it('should handle daily run pattern correctly', () => {
    const startDate = '2025-07-09';
    const endDate = '2025-07-10';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.DCS,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/DCS/2025/07/10/EMSP_DCS_INV_20250709.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/DCS/upload_date=2025-07-10/EMSP_DCS_INV_20250709.csv',
    );
  });

  it('should handle re-generation pattern correctly', () => {
    const startDate = '2025-03-15';
    const endDate = '2025-03-16';
    const { dataHubKey, emspKey } = constructFileKey(
      PROVIDER.HASTOBE,
      startDate,
      endDate,
    );

    expect(dataHubKey).toBe(
      'apollo/emsp/daily_sales_invoices/HTB/2025/03/16/EMSP_HTB_INV_20250315.csv.gz',
    );
    expect(emspKey).toBe(
      'Sales_invoice/Invoices/HTB/upload_date=2025-03-16/EMSP_HTB_INV_20250315.csv',
    );
  });

  it('should throw error for invalid start date', () => {
    const invalidStartDate = 'invalid-date';
    const validEndDate = '2023-03-15';
    expect(() =>
      constructFileKey(PROVIDER.DCS, invalidStartDate, validEndDate),
    ).toThrow('Invalid date for file key construction process: Invalid Date');
  });

  it('should throw error for invalid end date', () => {
    const validStartDate = '2023-03-14';
    const invalidEndDate = 'invalid-date';
    expect(() =>
      constructFileKey(PROVIDER.DCS, validStartDate, invalidEndDate),
    ).toThrow('Invalid date for path construction process: Invalid Date');
  });

  it('should handle unexpected provider gracefully', () => {
    const startDate = '2023-03-14';
    const endDate = '2023-03-15';
    const invalidProvider = 'UNKNOWN_PROVIDER' as PROVIDER;

    expect(() => constructFileKey(invalidProvider, startDate, endDate)).toThrow(
      'Invalid provider in file key construction process: UNKNOWN_PROVIDER',
    );
  });
});
