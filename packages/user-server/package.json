{"name": "@bp/feature-user-server", "version": "0.0.1", "private": true, "main": "./dist/index.js", "dependencies": {"@apollo/subgraph": "^2.1.3", "@aws-sdk/client-ses": "^3.632.0", "@bp/pulse-shared-types": "1.0.2", "@mailchimp/mailchimp_transactional": "^1.0.47", "apollo-server": "^3.10.2", "apollo-server-core": "^3.10.2", "apollo-server-express": "^3.10.2", "aws-sdk": "^2.1154.0", "axios": "^1.10.0", "date-fns": "^2.24.0", "dotenv": "^10.0.0", "graphql": "^16.6.0", "graphql-constraint-directive": "^4.1.2", "graphql-middleware": "^6.1.33", "graphql-request": "^3.5.0", "graphql-shield": "^7.6.5", "ioredis": "^5.0.6", "pg": "^8.7.1", "schemaglue": "^4.1.0", "winston": "^3.11.0"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/preset-env": "^7.23.9", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.14.6", "@bp/eslint-plugin": "^0.0.3", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.0.9", "@types/ioredis": "^4.26.5", "@types/jest": "^29.5.0", "@types/mailchimp__mailchimp_transactional": "^1.0.5", "@types/pg": "^8.6.1", "@types/sinonjs__fake-timers": "^8.1.5", "babel-jest": "^29.5.0", "eslint": "^8.20.0", "jest": "^29.5.0", "nodemon": "^2.0.20", "prettier": "2.7.1", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}