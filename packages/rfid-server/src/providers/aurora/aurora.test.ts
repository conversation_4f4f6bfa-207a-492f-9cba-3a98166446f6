import {
  Countries,
  TagNotes,
  TagStatus,
  UserStatus,
  UserTypes,
} from '../../common/enums';
import {
  DefaultRfidResponse,
  Providers,
  TagProviderStatus,
} from '../../common/interfaces';
import logger from '../../utils/logger';
import {
  addRFIDAurora,
  blockRFIDAurora,
  requestRFIDAurora,
  unblockRFIDAurora,
} from './aurora';
import {
  address,
  blockRFIDSuccessPayload,
  mockGetUserInfoSuccessContent,
  requestFailedRFIDPayload,
  requestSuccessRFIDPayloadNL,
  requestSuccessRFIDPayloadUK,
  userInfoPayload,
} from './aurora.test.mockdata';

const mockDcsAddPhysicalRfidCard = jest.fn();
const mockDcsAssociateRfid = jest.fn();
const mockDcsBlockRfid = jest.fn();
const mockDcsUnblockRfid = jest.fn();
const mockCreatePhysicalAuthMediaHtb = jest.fn();
const mockUpdatePhysicalAuthMediaHtb = jest.fn();
const mockDeprecatedRequestRFIDBPCM = jest.fn();
const mockGetEntriesByCardNumber = jest.fn();
const mockGetEntriesByStatus = jest.fn();
const mockUpdateEntriesByCardNumber = jest.fn();
const mockDel = jest.fn();
const mockInsertRFIDEntry = jest.fn();
const mockGetUserInfo = jest.fn();
const mockUpdateTagInternalStatus = jest.fn();
const mockupsertRFIDTagProviderInternal = jest.fn();
const mockAddRoaming = jest.fn();
const mockPaymentMethodsWallet = jest.fn();
const mockRecursiveCardGenerator = jest.fn();
const mockAddNewToken = jest.fn();
const mockUpdateToken = jest.fn();

jest.mock('../../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
}));

jest.mock('axios');

jest.mock('../../env.ts', () => ({
  NODE_ENV: 'production',
  OCPI_IDENTIFIER: 'ocpi-test-provider-dev',
}));

jest.mock('../../services/dcs', () => ({
  dcsAddPhysicalRfidCard: jest.fn((...args: unknown[]) =>
    mockDcsAddPhysicalRfidCard(...args),
  ),
  dcsAssociateRfid: jest.fn((...args: unknown[]) =>
    mockDcsAssociateRfid(...args),
  ),
  dcsBlockRfid: jest.fn((...args: unknown[]) => mockDcsBlockRfid(...args)),
  dcsUnblockRfid: jest.fn((...args: unknown[]) => mockDcsUnblockRfid(...args)),
}));

jest.mock('../../services/htb', () => ({
  createPhysicalAuthMediaHtb: jest.fn((...args: unknown[]) =>
    mockCreatePhysicalAuthMediaHtb(...args),
  ),
  updatePhysicalAuthMediaHtb: jest.fn((...args: unknown[]) =>
    mockUpdatePhysicalAuthMediaHtb(...args),
  ),
}));

jest.mock('../bpcm/bpcm', () => ({
  deprecatedRequestRFIDBPCM: jest.fn((...args: unknown[]) =>
    mockDeprecatedRequestRFIDBPCM(...args),
  ),
}));

jest.mock('../../services/ocpiService/ocpiService', () => ({
  addNewToken: jest.fn((...args: unknown[]) => mockAddNewToken(...args)),
  updateToken: jest.fn((...args: unknown[]) => mockUpdateToken(...args)),
}));

jest.mock('../../database/dynamo.client', () => ({
  getEntriesByCardNumber: jest.fn((...args: unknown[]) =>
    mockGetEntriesByCardNumber(...args),
  ),
  getEntriesByStatus: jest.fn((...args: unknown[]) =>
    mockGetEntriesByStatus(...args),
  ),
  updateEntriesByCardNumber: jest.fn((...args: unknown[]) =>
    mockUpdateEntriesByCardNumber(...args),
  ),
  del: jest.fn((...args: unknown[]) => mockDel(...args)),
  insertRFIDEntry: jest.fn((...args: unknown[]) =>
    mockInsertRFIDEntry(...args),
  ),
}));

jest.mock('../../services/userService/userService', () => ({
  getUserInfo: jest.fn((...args: unknown[]) => mockGetUserInfo(...args)),
  updateTagInternalStatus: jest.fn((...args: unknown[]) =>
    mockUpdateTagInternalStatus(...args),
  ),
  addRoaming: jest.fn((...args: unknown[]) => mockAddRoaming(...args)),
  upsertRFIDTagProviderInternal: jest.fn((...args: unknown[]) =>
    mockupsertRFIDTagProviderInternal(...args),
  ),
}));

jest.mock('../../services/walletService/walletService', () => ({
  paymentMethodsWallet: jest.fn((...args: unknown[]) =>
    mockPaymentMethodsWallet(...args),
  ),
}));

jest.mock('../../utils/rfidCardGenerator', () => ({
  recursiveCardGenerator: jest.fn((...args: unknown[]) =>
    mockRecursiveCardGenerator(...args),
  ),
}));

const ADD_RFID_MESSAGE = 'RFID successfully processed based on entitlements';
const REQUEST_TO_BLOCK_MESSAGE = 'Request to Block';
const RFID_BLOCKED_MESSAGE = 'RFID successfully blocked';
const RFID_UNBLOCKED_MESSAGE = 'Successfully unblocked RFID';

const TEST_LOG_TRACE_ID = 'test-log-trace-id';
const TEST_TOKEN = 'test-token';
const PAYG_WALLET = 'PAYG-WALLET';

beforeEach(() => {
  mockDcsAddPhysicalRfidCard.mockResolvedValue(200);
  mockDcsAssociateRfid.mockResolvedValue('DE*DC2*004XC4*3');
  mockDcsBlockRfid.mockResolvedValue(204);
  mockDcsUnblockRfid.mockResolvedValue('');
  mockCreatePhysicalAuthMediaHtb.mockResolvedValue({
    status: 'ok',
    message: 'The record was saved.',
  });
  mockUpdatePhysicalAuthMediaHtb.mockResolvedValue('');
  mockDeprecatedRequestRFIDBPCM.mockResolvedValue({
    status: 200,
    data: { eventDetails: '', eventTime: '', salesforceID: '' },
  });
  mockGetEntriesByCardNumber.mockResolvedValue('');
  mockInsertRFIDEntry.mockResolvedValue({
    status: 200,
  });
  mockGetUserInfo.mockResolvedValue(mockGetUserInfoSuccessContent);
  mockUpdateTagInternalStatus.mockResolvedValue(undefined);
  mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
  mockAddRoaming.mockResolvedValue(undefined);
  mockPaymentMethodsWallet.mockResolvedValue([
    {
      default: true,
    },
  ]);
  mockRecursiveCardGenerator.mockResolvedValue('dummyCardNumber');
  mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });
  mockUpdateToken.mockResolvedValue(undefined);
});

describe('Request RFID Data', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockAddRoaming.mockReset();
  });

  it('Request RFID successful scenario for Netherlands country', async () => {
    mockGetUserInfo.mockResolvedValue(userInfoPayload);
    const userInfo = await mockGetUserInfo(
      userInfoPayload.userId,
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );
    const result = await requestRFIDAurora(
      requestSuccessRFIDPayloadNL,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual({
      status: 200,
      data: { eventDetails: '', eventTime: '', salesforceID: 'dummyuserID' },
    });
  });

  it('Request RFID fails if userId is null', async () => {
    const result = requestRFIDAurora(
      requestFailedRFIDPayload as never,
      null,
      TEST_LOG_TRACE_ID,
    );
    await expect(result).rejects.toThrow(
      new Error(
        `no valid userId or address provided, userId: ${requestFailedRFIDPayload.userId}, address: ${requestFailedRFIDPayload.address}`,
      ),
    );
  });

  it('should return a success message when the user has positive balance', async () => {
    const balance = 3;
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance,
        roamingEnabled: true,
        country: Countries.NL,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
      }),
    );

    const userInfo = await mockGetUserInfo(
      'dummyuserID',
      Countries.NL,
      TEST_LOG_TRACE_ID,
    );
    const result = await requestRFIDAurora(
      requestSuccessRFIDPayloadNL,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual({
      data: { eventDetails: '', eventTime: '', salesforceID: 'dummyuserID' },
      status: 200,
    });
  });

  it('fails if balance is negative where country: UK, type: PAYG_Wallet', async () => {
    const balance = -7;
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance,
        roamingEnabled: true,
        country: Countries.UK,
        type: UserTypes.PAYG_Wallet,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
      }),
    );

    const userInfo = await mockGetUserInfo(
      'dummyuserID',
      Countries.UK,
      TEST_LOG_TRACE_ID,
    );
    const result = await requestRFIDAurora(
      requestSuccessRFIDPayloadUK,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual(new Error('user has an outstanding balance -7'));
  });

  it('should return an error when the get payment methods information is rejected', async () => {
    mockGetUserInfo.mockResolvedValueOnce(userInfoPayload);
    mockPaymentMethodsWallet.mockImplementationOnce(() =>
      Promise.reject('wallet server error'),
    );

    const userInfo = await mockGetUserInfo(
      userInfoPayload.userId,
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );

    await expect(
      requestRFIDAurora(
        requestSuccessRFIDPayloadNL,
        userInfo,
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toEqual('wallet server error');
  });

  it('should return an error when the user has no default payment method', async () => {
    mockPaymentMethodsWallet.mockImplementationOnce(() => Promise.resolve([]));
    mockGetUserInfo.mockResolvedValue(userInfoPayload);

    const userInfo = await mockGetUserInfo(
      userInfoPayload.userId,
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );
    const promise = requestRFIDAurora(
      requestSuccessRFIDPayloadNL,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    await expect(promise).rejects.toEqual(
      new Error('user has no default payment method'),
    );
  });

  it('should return an error when the recursive card generator does not return card number', async () => {
    mockGetUserInfo.mockResolvedValueOnce(userInfoPayload);
    mockRecursiveCardGenerator.mockImplementationOnce(() =>
      Promise.reject('card generator error'),
    );

    const userInfo = await mockGetUserInfo(
      userInfoPayload.userId,
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );

    await expect(
      requestRFIDAurora(
        requestSuccessRFIDPayloadNL,
        userInfo,
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toEqual('card generator error');
  });

  it('should call requestRFIDBPCM for UK PAYG Wallet user', async () => {
    jest.clearAllMocks();

    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        roamingEnabled: true,
        country: Countries.UK,
        type: UserTypes.SUBS,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
      }),
    );

    const payload = {
      userId: '12345667891919282',
      address: {
        ...address,
        addressCountry: Countries.UK,
      },
      type: 'bpcm',
      country: Countries.UK,
    };

    const userInfo = await mockGetUserInfo(
      payload.userId,
      Countries.UK,
      TEST_LOG_TRACE_ID,
    );
    await requestRFIDAurora(payload, userInfo, TEST_LOG_TRACE_ID);

    expect(mockDeprecatedRequestRFIDBPCM).toHaveBeenCalledWith(
      payload,
      TEST_LOG_TRACE_ID,
    );
  });
});

describe('Add RFID', () => {
  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.ENABLE_HTB_UK;
  });

  it('should successfully add RFID when all conditions are met for HASTOBE revenue plan', async () => {
    mockGetUserInfo.mockResolvedValue(mockGetUserInfoSuccessContent);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValueOnce(undefined);
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });

    expect(mockGetUserInfo).toHaveBeenCalledWith(
      '12345',
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );
    expect(mockCreatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
      'test',
      Countries.DE,
      '7777',
      'HTB Uber',
      '1',
      TEST_LOG_TRACE_ID,
      expect.anything(),
    );
  });

  it('should process RFID successfully', async () => {
    const mockDataWithZeroBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: 0,
    };
    mockGetUserInfo.mockResolvedValue(mockDataWithZeroBalance);

    const result = await addRFIDAurora(
      {
        userId: 'user123',
        country: Countries.UK,
        cardNumber: '1234',
        cardUid: 'uid123',
      },
      'trace123',
    );

    expect(result).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });
  });

  it('should successfully process multiple providers (HASTOBE and BPCM)', async () => {
    const mockUserInfoWithBothProviders = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoWithBothProviders);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
    mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });

    expect(mockCreatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
      'test',
      Countries.DE,
      '7777',
      'HTB Uber',
      '1',
      TEST_LOG_TRACE_ID,
      expect.anything(),
    );

    expect(mockAddNewToken).toHaveBeenCalled();
    expect(mockupsertRFIDTagProviderInternal).toHaveBeenCalledTimes(3);
    expect(mockupsertRFIDTagProviderInternal).toHaveBeenLastCalledWith(
      {
        tagId: mockUserInfoWithBothProviders.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.HASTOBE, Providers.CHARGEVISION],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should process HASTOBE provider for UK when ENABLE_HTB_UK is true', async () => {
    process.env.ENABLE_HTB_UK = 'true';

    const mockUserInfoUK = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: Providers.HASTOBE,
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoUK);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.UK,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });
    expect(mockCreatePhysicalAuthMediaHtb).toHaveBeenCalled();
  });

  it('should skip HASTOBE provider for UK when ENABLE_HTB_UK is false', async () => {
    process.env.ENABLE_HTB_UK = 'false';

    const mockUserInfoUK = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: Providers.HASTOBE,
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoUK);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.UK,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });
    expect(mockCreatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
  });

  it('should successfully process CHARGEVISION provider only', async () => {
    const mockUserInfoBPCM = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoBPCM);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
    mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.UK,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });
    expect(mockAddNewToken).toHaveBeenCalled();
    expect(mockupsertRFIDTagProviderInternal).toHaveBeenCalledWith(
      {
        tagId: mockUserInfoBPCM.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.CHARGEVISION],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should handle CHARGEVISION provider failure and continue with other providers', async () => {
    const mockUserInfoMultiProvider = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);
    mockCreatePhysicalAuthMediaHtb.mockRejectedValue(
      new Error('HASTOBE API Error'),
    );
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
    mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });

    expect(mockupsertRFIDTagProviderInternal).toHaveBeenCalledTimes(2);
    expect(mockupsertRFIDTagProviderInternal).toHaveBeenLastCalledWith(
      {
        tagId: mockUserInfoMultiProvider.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.CHARGEVISION],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should handle BPCM provider failure and continue with other providers', async () => {
    const mockUserInfoMultiProvider = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
    mockAddNewToken.mockRejectedValue(new Error('BPCM API Error'));

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });

    expect(mockupsertRFIDTagProviderInternal).toHaveBeenCalledTimes(2);
    expect(mockupsertRFIDTagProviderInternal).toHaveBeenLastCalledWith(
      {
        tagId: mockUserInfoMultiProvider.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.HASTOBE],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should process successfully when no matching providers found', async () => {
    const mockUserInfoNoMatchingProviders = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: ['INVALID_PROVIDER'],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoNoMatchingProviders);

    const result = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual({
      status: 200,
      message: 'RFID successfully processed based on entitlements',
    });

    expect(mockCreatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(mockAddNewToken).not.toHaveBeenCalled();
    expect(mockupsertRFIDTagProviderInternal).not.toHaveBeenCalled();
  });

  it('should call updateTagProviderInternal with correct parameters for HASTOBE', async () => {
    const mockUserInfoHTB = {
      ...mockGetUserInfoSuccessContent,
      tagStatus: TagProviderStatus.ACTIVE,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: Providers.HASTOBE,
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoHTB);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockupsertRFIDTagProviderInternal).toHaveBeenCalledWith(
      {
        tagId: mockUserInfoHTB.tagIds[0].tagInternalId,
        providers: [Providers.HASTOBE],
        tagProviderStatus: TagProviderStatus.ACTIVE,
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should call upsertRFIDTagProviderInternal with successful providers only', async () => {
    const mockUserInfoMultiProvider = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [
          Providers.HASTOBE,
          Providers.CHARGEVISION,
          'INVALID_PROVIDER',
        ],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockupsertRFIDTagProviderInternal).toHaveBeenLastCalledWith(
      {
        tagId: mockUserInfoMultiProvider.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.HASTOBE, Providers.CHARGEVISION],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should not call final upsertRFIDTagProviderInternal when no providers succeed', async () => {
    const mockUserInfoFailingProviders = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoFailingProviders);
    mockCreatePhysicalAuthMediaHtb.mockRejectedValue(
      new Error('HASTOBE Error'),
    );
    mockAddNewToken.mockRejectedValue(new Error('BPCM Error'));

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockupsertRFIDTagProviderInternal).not.toHaveBeenCalled();
  });

  it('should call final upsertRFIDTagProviderInternal only when at least one provider succeeds', async () => {
    const mockUserInfoMixedResults = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoMixedResults);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockAddNewToken.mockRejectedValue(new Error('BPCM Error'));

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockupsertRFIDTagProviderInternal).toHaveBeenLastCalledWith(
      {
        tagId: mockUserInfoMixedResults.tagIds[0].tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE,
        providers: [Providers.HASTOBE],
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should return 400 when user status is not ACTIVE', async () => {
    const mockUserInfoInactive = {
      ...mockGetUserInfoSuccessContent,
      status: UserStatus.BLOCKED,
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoInactive);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 400,
      message: `User status is not ACTIVE for userId: 12345, current status: ${UserStatus.BLOCKED}`,
    });
  });

  it('should return 400 when user status is BLOCKED', async () => {
    const mockUserInfoBlocked = {
      ...mockGetUserInfoSuccessContent,
      status: 'BLOCKED',
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoBlocked);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 400,
      message:
        'User status is not ACTIVE for userId: 12345, current status: BLOCKED',
    });
  });

  it('should return 400 when user status is undefined', async () => {
    const mockUserInfoUndefined = {
      ...mockGetUserInfoSuccessContent,
      status: undefined,
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoUndefined);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 400,
      message:
        'User status is not ACTIVE for userId: 12345, current status: undefined',
    });
  });

  it('should return 402 when user has negative balance and not call HTB providers', async () => {
    const mockDataWithNegativeBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: -10,
    };
    mockGetUserInfo.mockResolvedValue(mockDataWithNegativeBalance);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);

    await expect(
      addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'Error in addRFIDAurora: Add RFID failed, balance: -10 is not valid for userId: 12345',
    );

    expect(mockGetUserInfo).toHaveBeenCalledWith(
      '12345',
      Countries.DE,
      TEST_LOG_TRACE_ID,
    );
    expect(mockUpdateTagInternalStatus).toHaveBeenCalledWith(
      {
        country: Countries.DE,
        salesforceId: '12345',
        tagSerialNumber: 'test',
        tagCardNumber: '7777',
        tagStatus: TagStatus.NO_CREDIT,
      },
      TEST_LOG_TRACE_ID,
    );
    expect(mockCreatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
  });

  it('should return 402 when balance is negative', async () => {
    const mockUserInfoNegativeBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: -50,
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoNegativeBalance);

    await expect(
      addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'Error in addRFIDAurora: Add RFID failed, balance: -50 is not valid for userId: 12345',
    );

    expect(mockUpdateTagInternalStatus).toHaveBeenCalledWith(
      {
        country: Countries.DE,
        salesforceId: '12345',
        tagSerialNumber: 'test',
        tagCardNumber: '7777',
        tagStatus: TagStatus.NO_CREDIT,
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should return 402 when balance is undefined', async () => {
    const mockUserInfoUndefinedBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: undefined,
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoUndefinedBalance);

    await expect(
      addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'Error in addRFIDAurora: Add RFID failed, balance: undefined is not valid for userId: 12345',
    );
  });

  it('should succeed when balance is zero', async () => {
    const mockUserInfoZeroBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: 0,
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoZeroBalance);

    const response = await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: ADD_RFID_MESSAGE,
    });
  });

  it('should not process any providers when balance is negative', async () => {
    const mockUserInfoNegativeBalance = {
      ...mockGetUserInfoSuccessContent,
      balance: -100,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoNegativeBalance);

    await expect(
      addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'Error in addRFIDAurora: Add RFID failed, balance: -100 is not valid for userId: 12345',
    );

    expect(mockCreatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(mockAddNewToken).not.toHaveBeenCalled();
    expect(mockupsertRFIDTagProviderInternal).not.toHaveBeenCalled();
  });

  it('should create new tag with ACTIVE status when tag does not exist', async () => {
    const mockUserInfoNoExistingTag = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      tagIds: [
        {
          tagInternalId: 1,
          tagId: 'some-other-tag',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '9999',
        },
      ],
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoNoExistingTag);

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockUpdateTagInternalStatus).toHaveBeenNthCalledWith(
      1,
      {
        tagStatus: TagStatus.CUSTOMER_REQUESTED,
        tagTypeName: 'physical',
        tagCategoryName: 'RFID',
        tagNotes: TagNotes.PHYSICAL_RFID,
        tagCardNumber: '7777',
        country: Countries.DE,
        salesforceId: '12345',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockUpdateTagInternalStatus).toHaveBeenNthCalledWith(
      2,
      {
        country: Countries.DE,
        salesforceId: '12345',
        tagCardNumber: '7777',
        tagSerialNumber: 'test',
        tagStatus: TagStatus.ACTIVE,
        tagTypeName: 'physical',
      },
      TEST_LOG_TRACE_ID,
    );
  });

  it('should not create new tag when tag already exists', async () => {
    const mockUserInfoExistingTag = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      tagIds: [
        {
          tagInternalId: 43,
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
    };

    mockGetUserInfo.mockResolvedValue(mockUserInfoExistingTag);

    await addRFIDAurora(
      {
        userId: '12345',
        country: Countries.DE,
        cardNumber: '7777',
        cardUid: 'test',
      },
      TEST_LOG_TRACE_ID,
    );
    expect(mockUpdateTagInternalStatus).not.toHaveBeenCalledWith(
      expect.objectContaining({
        tagStatus: TagStatus.ACTIVE,
        tagTypeName: 'physical',
        tagCategoryName: 'RFID',
        tagNotes: TagNotes.PHYSICAL_RFID,
        tagCardNumber: '7777',
        country: Countries.DE,
        salesforceId: '12345',
      }),
      TEST_LOG_TRACE_ID,
    );
  });
});

describe('Block RFID', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    delete process.env.ENABLE_HTB_UK;
  });

  afterEach(() => {
    delete process.env.ENABLE_HTB_UK;
  });

  const getBlockPayload = (country: Countries = Countries.UK) => ({
    userId: '12345',
    country: country,
    reasonForBlocking: REQUEST_TO_BLOCK_MESSAGE,
    cardUid: 'test',
    cardNumber: '7777',
  });

  const getUserInfoForBlock = (country: Countries) => ({
    ...mockGetUserInfoSuccessContent,
    balance: 7,
    roamingEnabled: false,
    country: country,
    tagIds: [
      {
        tagId: 'test',
        tagCategoryName: 'RFID',
        tagTypeName: 'physical',
        tagNotes: TagNotes.PHYSICAL_RFID,
        tagStatus: TagStatus.ACTIVE,
        tagCardNumber: '7777',
      },
    ],
    type: UserTypes.PAYG_Wallet,
  });

  it('should run successfully for UK users', async () => {
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance: 7,
        roamingEnabled: false,
        country: Countries.UK,
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      }),
    );

    const response = await blockRFIDAurora(
      {
        userId: '12345',
        country: Countries.UK,
        reasonForBlocking: REQUEST_TO_BLOCK_MESSAGE,
        cardUid: 'test',
        cardNumber: '7777',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
    expect(logger.info).toHaveBeenCalledWith(
      `${RFID_BLOCKED_MESSAGE} for user: 12345, cardNumber: 7777`,
    );
  });

  it('should run successfully', async () => {
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance: 7,
        roamingEnabled: false,
        country: Countries.NL,
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      }),
    );
    const response = await blockRFIDAurora(
      blockRFIDSuccessPayload,
      TEST_LOG_TRACE_ID,
    );
    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
  });

  it('should be unable to update the tags if no physical dcs tag', async () => {
    const response = await blockRFIDAurora(
      blockRFIDSuccessPayload,
      TEST_LOG_TRACE_ID,
    );
    expect(response).toEqual({
      status: 400,
      message:
        'RFID could not be blocked. No physical card associated with userId: 12345',
    });
  });

  it('should be able to update the tags if updatePhysicalAuthMediaHtb fails', async () => {
    jest.clearAllMocks();
    const err = new Error('Failed to update physical authMedia on HASTOBE');
    mockGetUserInfo.mockResolvedValueOnce({
      ...mockGetUserInfoSuccessContent,
      roamingEnabled: false,
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
      type: UserTypes.PAYG_Wallet,
    });
    mockUpdatePhysicalAuthMediaHtb.mockReset();
    mockUpdatePhysicalAuthMediaHtb.mockRejectedValue(err);
    await expect(
      blockRFIDAurora(blockRFIDSuccessPayload, TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      `Failed to call updatePhysicalAuthMediaHtb to mark the tag as blocked for userId 12345, error ${err}`,
    );
  });

  it('should be unable to update the tags if updateTagInternalStatus fails update tags', async () => {
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve({
        ...mockGetUserInfoSuccessContent,
        balance: 5,
        roamingEnabled: false,
        country: Countries.DE,
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      }),
    );
    const updateError = new Error('Database update failed');
    mockUpdateTagInternalStatus.mockRejectedValueOnce(updateError);
    await expect(
      blockRFIDAurora(blockRFIDSuccessPayload, TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      `Failed to call updateTagInternalStatus to mark the tag as blocked for userId 12345, error ${updateError}`,
    );
  });

  it('should be unable to update the tags if getUserInfo fails', async () => {
    const err = new Error('Bad request');
    mockGetUserInfo.mockRejectedValueOnce(err);
    await expect(
      blockRFIDAurora(blockRFIDSuccessPayload, TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      `Failed to get tagIds from getUserInfo for userId 12345, error ${err}`,
    );
  });

  it('should insert blocked data in dynamo if requestToBlock is Request to Cancel', async () => {
    jest.clearAllMocks();

    mockGetUserInfo.mockResolvedValueOnce({
      ...mockGetUserInfoSuccessContent,
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
      type: UserTypes.PAYG_Wallet,
      partnerType: 'TEST_PARTNER',
    });

    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockUpdatePhysicalAuthMediaHtb.mockImplementation(() => Promise.resolve());
    mockInsertRFIDEntry.mockImplementation(() =>
      Promise.resolve({ status: 200 }),
    );

    const blockRFIDRequestToCancel = {
      ...blockRFIDSuccessPayload,
      reasonForBlocking: 'Request to Cancel',
    };

    await blockRFIDAurora(blockRFIDRequestToCancel, TEST_LOG_TRACE_ID);
    expect(mockInsertRFIDEntry).toHaveBeenCalledTimes(1);
  });

  it('should throw error if data is not inserted in Dynamo as part of the blockRFID call', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      ...mockGetUserInfoSuccessContent,
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
      type: UserTypes.PAYG_Wallet,
      partnerType: 'TEST_PARTNER',
    });

    const blockRFIDRequestToCancel = {
      ...blockRFIDSuccessPayload,
      reasonForBlocking: 'Request to Cancel',
    };
    mockInsertRFIDEntry.mockImplementationOnce(() =>
      Promise.reject('Failed to insert rfid data'),
    );
    await expect(
      blockRFIDAurora(blockRFIDRequestToCancel, TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      `Failed to insert PENDING_TERMINATION data into Dynamo for cardNumber: 7777 and tagStatus: ${TagStatus.PENDING_TERMINATION}, error Failed to insert rfid data`,
    );
  });

  it('should process HASTOBE for UK users when ENABLE_HTB_UK is true', async () => {
    process.env.ENABLE_HTB_UK = 'true';

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve(getUserInfoForBlock(Countries.UK)),
    );
    mockUpdatePhysicalAuthMediaHtb.mockResolvedValue(undefined);

    const response = await blockRFIDAurora(
      getBlockPayload(Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `blockRFID: Updating HASTOBE for user in UK (ENABLE_HTB_UK: true)`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
      'test',
      '0',
      '7777',
      TEST_LOG_TRACE_ID,
      true,
    );
    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
  });

  it('should skip HASTOBE for UK users when ENABLE_HTB_UK is false', async () => {
    process.env.ENABLE_HTB_UK = 'false';

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve(getUserInfoForBlock(Countries.UK)),
    );

    const response = await blockRFIDAurora(
      getBlockPayload(Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `blockRFID: HASTOBE processing skipped for UK user 12345 - ENABLE_HTB_UK feature flag is disabled`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
  });

  it('should skip HASTOBE for UK users when ENABLE_HTB_UK is undefined', async () => {
    delete process.env.ENABLE_HTB_UK;

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve(getUserInfoForBlock(Countries.UK)),
    );

    const response = await blockRFIDAurora(
      getBlockPayload(Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `blockRFID: HASTOBE processing skipped for UK user 12345 - ENABLE_HTB_UK feature flag is disabled`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
  });

  it('should always process HASTOBE for non-UK countries regardless of feature flag', async () => {
    process.env.ENABLE_HTB_UK = 'false';

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve(getUserInfoForBlock(Countries.DE)),
    );
    mockUpdatePhysicalAuthMediaHtb.mockResolvedValue(undefined);

    const response = await blockRFIDAurora(
      getBlockPayload(Countries.DE),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `blockRFID: Updating HASTOBE for user in DE (ENABLE_HTB_UK: false)`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalled();
    expect(response).toEqual({
      status: 200,
      message: RFID_BLOCKED_MESSAGE,
    });
  });

  it('should handle HASTOBE failure for UK users when ENABLE_HTB_UK is true', async () => {
    process.env.ENABLE_HTB_UK = 'true';

    const htbError = new Error('HASTOBE API Error');
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    mockGetUserInfo.mockImplementationOnce(() =>
      Promise.resolve(getUserInfoForBlock(Countries.UK)),
    );
    mockUpdatePhysicalAuthMediaHtb.mockRejectedValue(htbError);

    await expect(
      blockRFIDAurora(getBlockPayload(Countries.UK), TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      `Failed to call updatePhysicalAuthMediaHtb to mark the tag as blocked for userId 12345, error ${htbError}`,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `blockRFID: Updating HASTOBE for user in UK (ENABLE_HTB_UK: true)`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalled();
  });

  it('should handle CV-PAYG virtual tags in blockRFID', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
        {
          tagId: 'virtual-test',
          tagCategoryName: 'CV-PAYG',
          tagTypeName: 'virtual',
          tagNotes: TagNotes.VIRTUAL_CV_PAYG,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '8888',
        },
      ],
      type: UserTypes.PAYG_Wallet,
    };

    mockGetUserInfo.mockResolvedValue(userInfo);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockUpdatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdateToken.mockResolvedValue(undefined);

    const response = await blockRFIDAurora(
      {
        userId: '12345',
        country: Countries.UK,
        reasonForBlocking: 'Request to Block',
        cardUid: 'test',
        cardNumber: '7777',
      },
      TEST_LOG_TRACE_ID,
    );

    expect(mockUpdateToken).toHaveBeenCalled();
    expect(response).toEqual({
      status: 200,
      message: 'RFID successfully blocked',
    });
  });
});

describe('Request RFID', () => {
  it('should return error when RFID is not enabled', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: false,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        subsEnabled: true,
        partnerSchemes: [],
      },
    };

    const payload = {
      userId: 'test-user',
      country: Countries.DE,
      address,
    };

    const result = await requestRFIDAurora(
      payload,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual(
      new Error('RFID request not allowed for this region'),
    );
  });

  it('should handle payment methods with default false', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        partnerSchemes: [],
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        subsEnabled: true,
      },
    };

    mockGetUserInfo.mockResolvedValue(userInfo);
    mockPaymentMethodsWallet.mockResolvedValue([{ default: false }]);

    const payload = {
      userId: 'test-user',
      country: Countries.DE,
      address,
    };

    const result = await requestRFIDAurora(
      payload,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(result).toEqual({
      status: 200,
      data: { eventDetails: '', eventTime: '', salesforceID: 'test-user' },
    });
  });

  it('should update the tag status twice, to ACTIVE and then TERMINATED if the dynamo insert fails', async () => {
    mockUpdateTagInternalStatus
      .mockResolvedValueOnce(undefined)
      .mockResolvedValueOnce(undefined);

    mockInsertRFIDEntry.mockImplementationOnce(() =>
      Promise.reject('Failed to insert rfid data'),
    );

    const userInfo = await mockGetUserInfo(
      userInfoPayload.userId,
      Countries.NL,
      TEST_LOG_TRACE_ID,
    );
    const result = await requestRFIDAurora(
      requestSuccessRFIDPayloadNL,
      userInfo,
      TEST_LOG_TRACE_ID,
    );

    expect(mockUpdateTagInternalStatus).toHaveBeenCalledTimes(4);
    expect(result).toEqual({
      status: 200,
      data: { eventDetails: '', eventTime: '', salesforceID: 'dummyuserID' },
    });
  });

  it('should handle updateTagInternalStatus failure in requestRFID', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        subsEnabled: true,
        partnerSchemes: [],
      },
    };

    mockGetUserInfo.mockResolvedValue(userInfo);
    mockUpdateTagInternalStatus.mockRejectedValue(new Error('DB Error'));

    const payload = {
      userId: 'test-user',
      country: Countries.DE,
      address,
    };

    await expect(
      requestRFIDAurora(payload, userInfo, TEST_LOG_TRACE_ID),
    ).rejects.toThrow(
      'Failed to call updateTagInternalStatus to store RFID details',
    );
  });
});

describe('Unblock RFID', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    delete process.env.ENABLE_HTB_UK;
  });

  afterEach(() => {
    delete process.env.ENABLE_HTB_UK;
  });

  const getUserInfoPayload = (provider: string) => {
    return {
      ...mockGetUserInfoSuccessContent,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [PAYG_WALLET],
        rfidDefaultProviders: [
          Providers.HASTOBE,
          Providers.BPCM,
          Providers.DCS,
        ],
      },
      roamingEnabled: false,
      balance: 0,
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: provider,
          tagTypeName: 'virtual',
          tagNotes: `virtual-${provider}`,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
      type: UserTypes.PAYG_Wallet,
    };
  };

  const getPayload = (provider: string, country: Countries = Countries.UK) => {
    const cardNumber = `123456-${provider}-ACTIVE`;
    return {
      userId: 'testUserId',
      country: country,
      cardUid: cardNumber,
      cardNumber: cardNumber,
    };
  };

  const successfulResponse = {
    status: 200,
    message: RFID_UNBLOCKED_MESSAGE,
  };

  it('should successfully unblocks RFID when there are NO_CREDIT HASTOBE virtual card tags', async () => {
    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.DE),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for DE user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(result).toEqual(successfulResponse);
  });

  it('should successfully unblocks RFID when there are NO_CREDIT DCS virtual card tags', async () => {
    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('DCS'));

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    const result = await unblockRFIDAurora(
      getPayload('DCS'),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating DCS for non-UK user testUserId with cardNumber 123456-DCS-ACTIVE`,
    );
    expect(result).toEqual(successfulResponse);
  });

  it('should successfully unblocks RFID when there are NO_CREDIT PAYG virtual card', async () => {
    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('CV-PAYG'));

    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());
    const result = await unblockRFIDAurora(
      getPayload('CV-PAYG'),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating OCPI token for UK user testUserId with cardNumber 123456-CV-PAYG-ACTIVE`,
    );
    expect(result).toEqual(successfulResponse);
  });

  it('should successfully unblocks RFID when there are HASTOBE virtual card tags for Netherlands', async () => {
    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.NL),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for NL user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(result).toEqual(successfulResponse);
  });

  it('should successfully unblock RFID for UK users when ENABLE_HTB_UK is true', async () => {
    process.env.ENABLE_HTB_UK = 'true';

    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for UK user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalled();
    expect(result).toEqual(successfulResponse);
  });

  it('should skip HASTOBE processing for UK users when ENABLE_HTB_UK is false', async () => {
    process.env.ENABLE_HTB_UK = 'false';

    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for UK user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(result).toEqual(successfulResponse);
  });

  it('should skip HTB processing for UK users when ENABLE_HTB_UK is undefined', async () => {
    delete process.env.ENABLE_HTB_UK;

    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.UK),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for UK user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    expect(result).toEqual(successfulResponse);
  });

  it('should always process HTB for non-UK countries regardless of feature flag', async () => {
    process.env.ENABLE_HTB_UK = 'false';

    mockGetUserInfo.mockResolvedValueOnce(getUserInfoPayload('HTB'));
    mockUpdateTagInternalStatus.mockImplementation(() => Promise.resolve());

    const result = await unblockRFIDAurora(
      getPayload('HTB', Countries.DE),
      TEST_LOG_TRACE_ID,
    );

    expect(logger.info).toHaveBeenCalledWith(
      `unblockRFID: Updating HASTOBE for DE user testUserId with cardNumber 123456-HTB-ACTIVE`,
    );
    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalled();
    expect(result).toEqual(successfulResponse);
  });

  it('should handle physical card with PENDING_TERMINATION status', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: 10,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'RFID',
          tagTypeName: 'physical',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagStatus: TagStatus.PENDING_TERMINATION,
          tagCardNumber: '7777',
        },
      ],
    };

    mockGetUserInfo.mockResolvedValue(userInfo);

    await expect(
      unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'RFID card cannot be unblocked due to pending termination status',
    );
  });

  it('should handle negative balance in unblockRFID', async () => {
    const userInfo = {
      ...mockGetUserInfoSuccessContent,
      balance: -5,
      entitlements: {
        rfidEnabled: true,
        chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
        paymentMethods: [UserTypes.PAYG_Wallet],
        rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
      },
      tagIds: [
        {
          tagId: 'test',
          tagCategoryName: 'HTB',
          tagTypeName: 'virtual',
          tagNotes: TagNotes.VIRTUAL_HTB,
          tagStatus: TagStatus.ACTIVE,
          tagCardNumber: '7777',
        },
      ],
    };

    mockGetUserInfo.mockResolvedValue(userInfo);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);

    await expect(
      unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      ),
    ).rejects.toThrow(
      'UnblockRFID failed, balance: -5 is not valid for userId: testUserId',
    );
  });
});

describe('Helper Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockGetUserInfo.mockResolvedValue(mockGetUserInfoSuccessContent);
    mockUpdateTagInternalStatus.mockResolvedValue(undefined);
    mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
    mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
    mockUpdatePhysicalAuthMediaHtb.mockResolvedValue('');
    mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });
    mockUpdateToken.mockResolvedValue(undefined);
    mockInsertRFIDEntry.mockResolvedValue({ status: 200 });
    mockPaymentMethodsWallet.mockResolvedValue([{ default: true }]);
    mockRecursiveCardGenerator.mockResolvedValue('dummyCardNumber');
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.ENABLE_HTB_UK;
  });

  describe('setTagStatusAsNoCredit', () => {
    it('should update tag status to NO_CREDIT and throw error', async () => {
      const mockUserInfoNegativeBalance = {
        ...mockGetUserInfoSuccessContent,
        balance: -5,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoNegativeBalance);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      await expect(
        addRFIDAurora(
          {
            userId: '12345',
            country: Countries.DE,
            cardNumber: '7777',
            cardUid: 'test',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow(
        'Add RFID failed, balance: -5 is not valid for userId: 12345',
      );

      expect(mockUpdateTagInternalStatus).toHaveBeenCalledWith(
        {
          country: Countries.DE,
          salesforceId: '12345',
          tagSerialNumber: 'test',
          tagCardNumber: '7777',
          tagStatus: TagStatus.NO_CREDIT,
        },
        TEST_LOG_TRACE_ID,
      );
    });

    it('should handle updateTagInternalStatus failure in setTagStatusAsNoCredit', async () => {
      const mockUserInfoNegativeBalance = {
        ...mockGetUserInfoSuccessContent,
        balance: -10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoNegativeBalance);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus
        .mockResolvedValueOnce(undefined)
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('DB Error'))
        .mockResolvedValueOnce(undefined);

      await expect(
        addRFIDAurora(
          {
            userId: '12345',
            country: Countries.DE,
            cardNumber: '7777',
            cardUid: 'test',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow(
        'Error in addRFIDAurora: Add RFID failed, balance: -10 is not valid for userId: 12345',
      );
    });
  });

  describe('checkBlockedPhysicalRFID', () => {
    it('should return true when all tags are BLOCKED', async () => {
      mockGetUserInfo.mockResolvedValueOnce({
        ...mockGetUserInfoSuccessContent,
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.BLOCKED,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      });

      const response = await blockRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          reasonForBlocking: REQUEST_TO_BLOCK_MESSAGE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(response).toEqual({
        status: 400,
        message:
          'RFID could not be blocked. Physical card is already blocked for userId: 12345',
      });
    });

    it('should return true when all tags are PENDING_TERMINATION', async () => {
      mockGetUserInfo.mockResolvedValueOnce({
        ...mockGetUserInfoSuccessContent,
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.PENDING_TERMINATION,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      });

      const response = await blockRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          reasonForBlocking: REQUEST_TO_BLOCK_MESSAGE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(response).toEqual({
        status: 400,
        message:
          'RFID could not be blocked. Physical card is already blocked for userId: 12345',
      });
    });
  });

  describe('checkIfCardAssociated', () => {
    it('should return empty array when card is not associated', async () => {
      mockGetUserInfo.mockResolvedValueOnce({
        ...mockGetUserInfoSuccessContent,
        tagIds: [
          {
            tagId: 'different-id',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: 'different-card',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      });

      const response = await blockRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          reasonForBlocking: REQUEST_TO_BLOCK_MESSAGE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(response).toEqual({
        status: 400,
        message:
          'RFID could not be blocked. No card with cardNumber: 7777 cardUid: test associated with userId: 12345',
      });
    });
  });

  describe('getTags', () => {
    it('should correctly categorize HASTOBE virtual tags', async () => {
      mockUpdateTagInternalStatus.mockReset();
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      const mockUserInfoHASTOBE = {
        ...mockGetUserInfoSuccessContent,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [PAYG_WALLET],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
        balance: 0,
        tagIds: [
          {
            tagId: 'htb-tag',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      };

      mockGetUserInfo.mockResolvedValueOnce(mockUserInfoHASTOBE);

      const result = (await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'HASTOBE-tag',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      )) as DefaultRfidResponse;

      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating HASTOBE for DE user testUserId with cardNumber 7777',
      );
      expect(result.status).toBe(200);
    });

    it('should correctly categorize DCS virtual tags', async () => {
      const mockUserInfoDCS = {
        ...mockGetUserInfoSuccessContent,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [PAYG_WALLET],
          rfidDefaultProviders: [Providers.DCS],
        },
        balance: 0,
        tagIds: [
          {
            tagId: 'dcs-tag',
            tagCategoryName: 'DCS',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_DCS,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      };

      mockGetUserInfo.mockResolvedValueOnce(mockUserInfoDCS);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      const result = (await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'dcs-tag',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      )) as DefaultRfidResponse;

      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating DCS for non-UK user testUserId with cardNumber 7777',
      );
      expect(result.status).toBe(200);
    });

    it('should correctly categorize CV-PAYG virtual tags', async () => {
      const mockUserInfoPayg = {
        ...mockGetUserInfoSuccessContent,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [PAYG_WALLET],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
        balance: 0,
        tagIds: [
          {
            tagId: 'payg-tag',
            tagCategoryName: 'CV-PAYG',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_CV_PAYG,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      };

      mockGetUserInfo.mockResolvedValueOnce(mockUserInfoPayg);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      const result = (await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'payg-tag',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      )) as DefaultRfidResponse;

      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating OCPI token for UK user testUserId with cardNumber 7777',
      );
      expect(result.status).toBe(200);
    });
  });

  describe('Payload Helper Functions', () => {
    it('should test getInsertPhysicalTagPayload through requestRFIDAurora', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        country: Countries.DE,
        entitlements: {
          rfidEnabled: true,
          subsEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
          partnerSchemes: [],
        },
      };

      mockGetUserInfo.mockResolvedValue(userInfo);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockInsertRFIDEntry.mockResolvedValue({ status: 200 });

      await requestRFIDAurora(
        {
          userId: 'test-user',
          country: Countries.DE,
          address,
        },
        userInfo,
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdateTagInternalStatus).toHaveBeenNthCalledWith(
        1,
        {
          tagStatus: TagStatus.CUSTOMER_REQUESTED,
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagNotes: TagNotes.PHYSICAL_RFID,
          tagCardNumber: 'dummyCardNumber',
          country: Countries.DE,
          salesforceId: 'test-user',
        },
        TEST_LOG_TRACE_ID,
      );
    });

    it('should test getUpdateTagToActivePayload through addRFIDAurora', async () => {
      const mockUserInfoActive = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoActive);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test-uid',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdateTagInternalStatus).toHaveBeenLastCalledWith(
        {
          country: Countries.DE,
          salesforceId: '12345',
          tagCardNumber: '7777',
          tagSerialNumber: 'test-uid',
          tagStatus: TagStatus.ACTIVE,
          tagTypeName: 'physical',
        },
        TEST_LOG_TRACE_ID,
      );
    });
  });

  describe('notifyHASTOBEToUnblock', () => {
    it('should call updatePhysicalAuthMediaHtb with correct parameters', async () => {
      const mockUserInfoHASTOBE = {
        ...mockGetUserInfoSuccessContent,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [PAYG_WALLET],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
        },
        balance: 0,
        tagIds: [
          {
            tagId: 'HASTOBE-tag',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
        type: UserTypes.PAYG_Wallet,
      };

      mockGetUserInfo.mockResolvedValueOnce(mockUserInfoHASTOBE);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue('');

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test-card-uid',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        'test-card-uid',
        '1',
        '7777',
        TEST_LOG_TRACE_ID,
      );
    });
  });

  describe('shouldProcessHASTOBEInUK', () => {
    it('should return true for non-UK countries', async () => {
      const mockUserInfoDE = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoDE);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockCreatePhysicalAuthMediaHtb).toHaveBeenCalled();
    });

    it('should return true for UK when ENABLE_HTB_UK is true', async () => {
      process.env.ENABLE_HTB_UK = 'true';

      const mockUserInfoUK = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoUK);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.UK,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockCreatePhysicalAuthMediaHtb).toHaveBeenCalled();
    });

    it('should return false for UK when ENABLE_HTB_UK is false', async () => {
      process.env.ENABLE_HTB_UK = 'false';

      const mockUserInfoUK = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoUK);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.UK,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockCreatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'HASTOBE provider skipped for UK user: 12345 - feature flag ENABLE_HTB_UK is disabled',
      );
    });
  });

  describe('validateRFIDEntitlements', () => {
    it('should throw error when RFID is not enabled', async () => {
      const mockUserInfoDisabled = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: false,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoDisabled);

      await expect(
        addRFIDAurora(
          {
            userId: '12345',
            country: Countries.DE,
            cardNumber: '7777',
            cardUid: 'test',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow(
        'Error in addRFIDAurora: RFID not enabled for user with userId: 12345 in country: DE',
      );
    });

    it('should throw error when no tags present', async () => {
      const mockUserInfoNoTags = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        tagIds: [],
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoNoTags);

      await expect(
        addRFIDAurora(
          {
            userId: '12345',
            country: Countries.DE,
            cardNumber: '7777',
            cardUid: 'test',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow(
        'Error in addRFIDAurora: No tags present for userId: 12345 in country: DE',
      );
    });
  });

  describe('logProviderResults', () => {
    it('should log successful providers', async () => {
      const mockUserInfoMultiProvider = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.CHARGEVISION],
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);
      mockupsertRFIDTagProviderInternal.mockResolvedValue(undefined);
      mockAddNewToken.mockResolvedValue({ token: TEST_TOKEN });

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(logger.info).toHaveBeenCalledWith(
        'HASTOBE provider successfully processed for user: 12345, cardNumber: 7777',
      );
      expect(logger.info).toHaveBeenCalledWith(
        'Successful providers: HASTOBE, CHARGEVISION',
      );
    });

    it('should log failed providers', async () => {
      const mockUserInfoMultiProvider = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.CHARGEVISION],
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);
      mockCreatePhysicalAuthMediaHtb.mockRejectedValue(
        new Error('HASTOBE failure'),
      );
      mockAddNewToken.mockRejectedValue(new Error('BPCM failure'));
      mockUpdateTagInternalStatus.mockResolvedValue(undefined);

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(logger.error).toHaveBeenCalledWith(
        'HASTOBE provider failed for user: 12345, cardNumber: 7777, error: HASTOBE failure',
      );
      expect(logger.error).toHaveBeenCalledWith(
        'CHARGEVISION provider failed for user: 12345, cardNumber: 7777, error: BPCM failure',
      );
      expect(logger.warn).toHaveBeenCalledWith(
        'Failed providers for user 12345:',
        [
          { provider: Providers.HASTOBE, error: 'HASTOBE failure' },
          { provider: Providers.CHARGEVISION, error: 'BPCM failure' },
        ],
      );
      expect(logger.error).toHaveBeenCalledWith(
        'All providers failed. Failed providers: HASTOBE: HASTOBE failure, CHARGEVISION: BPCM failure',
      );
    });

    it('should log successful and failed providers', async () => {
      const mockUserInfoMultiProvider = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM], // Keep BPCM here as this is the config
        },
      };

      mockGetUserInfo.mockResolvedValue(mockUserInfoMultiProvider);
      mockCreatePhysicalAuthMediaHtb.mockResolvedValue(undefined);
      mockAddNewToken.mockRejectedValue(new Error('BPCM failed'));

      await addRFIDAurora(
        {
          userId: '12345',
          country: Countries.DE,
          cardNumber: '7777',
          cardUid: 'test',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(logger.info).toHaveBeenCalledWith('Successful providers: HASTOBE');

      expect(logger.warn).toHaveBeenCalledWith(
        'Failed providers for user 12345:',
        [{ provider: 'CHARGEVISION', error: 'BPCM failed' }],
      );
    });
  });

  describe('processUnblockingByProvider', () => {
    it('should process BPCM provider when cvPaygVirtualRFID is available', async () => {
      process.env.NODE_ENV = 'production';

      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.BPCM],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'CV-PAYG',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_CV_PAYG,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      const result = await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.UK,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdateToken).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating OCPI token for UK user testUserId with cardNumber 7777',
      );
      expect(result).toEqual({
        status: 200,
        message: RFID_UNBLOCKED_MESSAGE,
      });

      delete process.env.NODE_ENV;
    });

    it('should process HASTOBE provider when htbVirtualRFID is available', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      const result = await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating HASTOBE for DE user testUserId with cardNumber 7777',
      );
      expect(result).toEqual({
        status: 200,
        message: RFID_UNBLOCKED_MESSAGE,
      });
    });

    it('should process DCS provider when dcsVirtualRFID is available', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.DCS],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'DCS',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_DCS,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      const result = await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockDcsUnblockRfid).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        'unblockRFID: Updating DCS for non-UK user testUserId with cardNumber 7777',
      );
      expect(result).toEqual({
        status: 200,
        message: RFID_UNBLOCKED_MESSAGE,
      });
    });
  });

  describe('handleBPCMUnblocking', () => {
    it('should handle BPCM unblocking successfully', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.BPCM],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'CV-PAYG',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_CV_PAYG,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.UK,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdateToken).toHaveBeenCalledWith(
        {
          uid: 'test',
          ocpiIdentifier: 'chargeVision-PAYG',
          valid: true,
        },
        TEST_LOG_TRACE_ID,
      );
      expect(logger.info).toHaveBeenCalledWith(
        'onboardWithProvidersUK: called ocpi updateToken with ocpiIdentifier chargeVision-PAYG',
      );
    });

    it('should handle BPCM unblocking failure', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.BPCM],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'CV-PAYG',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_CV_PAYG,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);
      mockUpdateToken.mockRejectedValue(new Error('OCPI Error'));

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.UK,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(logger.error).toHaveBeenCalledWith(
        'failed updateToken for testUserId, ocpiIdentifier chargeVision-PAYG',
        expect.any(Error),
      );
    });
  });

  describe('handleHTBUnblocking', () => {
    it('should handle HASTOBE unblocking for non-UK countries', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        'test',
        '1',
        '7777',
        TEST_LOG_TRACE_ID,
      );
    });

    it('should handle HASTOBE unblocking for UK when feature flag is enabled', async () => {
      process.env.ENABLE_HTB_UK = 'true';

      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.UK,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        'test',
        '1',
        '7777',
        TEST_LOG_TRACE_ID,
      );
    });

    it('should skip HASTOBE processing for UK when feature flag is disabled', async () => {
      process.env.ENABLE_HTB_UK = 'false';

      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.UK,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
    });
  });

  describe('handleDCSUnblocking', () => {
    it('should handle DCS unblocking successfully', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.DCS],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'DCS',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_DCS,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(mockDcsUnblockRfid).toHaveBeenCalledWith(
        '7777',
        '7777',
        Countries.DE,
        TEST_LOG_TRACE_ID,
      );
    });

    it('should handle DCS unblocking failure', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: [Providers.DCS],
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'DCS',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_DCS,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);
      mockDcsUnblockRfid.mockRejectedValue(new Error('DCS Error'));

      await expect(
        unblockRFIDAurora(
          {
            userId: 'testUserId',
            country: Countries.DE,
            cardUid: 'test',
            cardNumber: '7777',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow('UnblockRFID failed due to an internal error');
    });
  });

  describe('hasValidRFID validation', () => {
    it('should throw error when no valid RFID types are available', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'RFID',
            tagTypeName: 'physical',
            tagNotes: TagNotes.PHYSICAL_RFID,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      await expect(
        unblockRFIDAurora(
          {
            userId: 'testUserId',
            country: Countries.DE,
            cardUid: 'test',
            cardNumber: '7777',
          },
          TEST_LOG_TRACE_ID,
        ),
      ).rejects.toThrow('No matching tags');

      expect(logger.error).toHaveBeenCalledWith(
        'unblockRFIDAurora: no matching tags for userId: testUserId',
      );
    });

    it('should succeed when at least one valid RFID type is available', async () => {
      const userInfo = {
        ...mockGetUserInfoSuccessContent,
        balance: 10,
        entitlements: {
          rfidEnabled: true,
          chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
          paymentMethods: [UserTypes.PAYG_Wallet],
          rfidDefaultProviders: Providers.HASTOBE,
        },
        tagIds: [
          {
            tagId: 'test',
            tagCategoryName: 'HTB',
            tagTypeName: 'virtual',
            tagNotes: TagNotes.VIRTUAL_HTB,
            tagStatus: TagStatus.ACTIVE,
            tagCardNumber: '7777',
          },
        ],
      };

      mockGetUserInfo.mockResolvedValue(userInfo);

      const result = await unblockRFIDAurora(
        {
          userId: 'testUserId',
          country: Countries.DE,
          cardUid: 'test',
          cardNumber: '7777',
        },
        TEST_LOG_TRACE_ID,
      );

      expect(result).toEqual({
        status: 200,
        message: RFID_UNBLOCKED_MESSAGE,
      });
    });
  });
});
