/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
};

export type Mutation = {
  __typename?: 'Mutation';
  addPaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  deletePaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  deleteSUBSDefaultPaymentMethodWallet?: Maybe<DeleteSubsDefPaymentMethodResponse>;
  setDefaultPaymentMethodWallet?: Maybe<Scalars['String']['output']>;
  storePaymentToken?: Maybe<StorePaymentTokenResponse>;
};

export type MutationAddPaymentMethodWalletArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  default?: InputMaybe<Scalars['Boolean']['input']>;
  subsDefaultFlag?: InputMaybe<Scalars['Boolean']['input']>;
  threeDS?: InputMaybe<ThreeDs>;
  token: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationDeletePaymentMethodWalletArgs = {
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationDeleteSubsDefaultPaymentMethodWalletArgs = {
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationSetDefaultPaymentMethodWalletArgs = {
  isSubsDefault?: InputMaybe<Scalars['Boolean']['input']>;
  paymentMethodId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationStorePaymentTokenArgs = {
  isDefault?: InputMaybe<Scalars['Boolean']['input']>;
  isSubsDefault?: InputMaybe<Scalars['Boolean']['input']>;
  token: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type PaymentMethod = {
  __typename?: 'PaymentMethod';
  /** @deprecated Returned as empty string for security */
  address1?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  address2?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  bin?: Maybe<Scalars['String']['output']>;
  cardType?: Maybe<Scalars['String']['output']>;
  cardholderName?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  city?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  country?: Maybe<Scalars['String']['output']>;
  default?: Maybe<Scalars['Boolean']['output']>;
  deleteDate?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  email?: Maybe<Scalars['String']['output']>;
  fingerprint?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  firstName?: Maybe<Scalars['String']['output']>;
  isExpired?: Maybe<Scalars['Boolean']['output']>;
  lastFour?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  lastName?: Maybe<Scalars['String']['output']>;
  month?: Maybe<Scalars['Int']['output']>;
  /** @deprecated Returned as empty string for security */
  nickname?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  paymentMethodToken?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  phoneNumber?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  postalCode?: Maybe<Scalars['String']['output']>;
  /** @deprecated Returned as empty string for security */
  state?: Maybe<Scalars['String']['output']>;
  subsDefault?: Maybe<Scalars['Boolean']['output']>;
  threeDSStatus?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type PaymentTokenResponse = {
  __typename?: 'PaymentTokenResponse';
  isDefault?: Maybe<Scalars['Boolean']['output']>;
  isSubsDefault?: Maybe<Scalars['Boolean']['output']>;
  token?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  getPaymentMethodsWallet?: Maybe<Array<Maybe<PaymentMethod>>>;
  getPaymentToken?: Maybe<PaymentTokenResponse>;
  getPendingDeletionPaymentMethods?: Maybe<
    Array<Maybe<GetPendingDeletionResponse>>
  >;
};

export type QueryGetPaymentMethodsWalletArgs = {
  userId: Scalars['String']['input'];
};

export type QueryGetPaymentTokenArgs = {
  userId: Scalars['String']['input'];
};

export type QueryGetPendingDeletionPaymentMethodsArgs = {
  endTime: Scalars['String']['input'];
  startTime: Scalars['String']['input'];
};

export type StorePaymentTokenResponse = {
  __typename?: 'StorePaymentTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ThreeDs = {
  acsTransactionId?: InputMaybe<Scalars['String']['input']>;
  cavv?: InputMaybe<Scalars['String']['input']>;
  dsTransactionId?: InputMaybe<Scalars['String']['input']>;
  eciFlag?: InputMaybe<Scalars['String']['input']>;
  enrolled?: InputMaybe<Scalars['String']['input']>;
  paresStatus?: InputMaybe<Scalars['String']['input']>;
  statusReason?: InputMaybe<Scalars['String']['input']>;
  threeDSServerTransactionId?: InputMaybe<Scalars['String']['input']>;
  threeDSVersion?: InputMaybe<Scalars['String']['input']>;
};

export type DeleteSubsDefPaymentMethodResponse = {
  __typename?: 'deleteSUBSDefPaymentMethodResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type GetPendingDeletionResponse = {
  __typename?: 'getPendingDeletionResponse';
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type GetPaymentMethodsWalletQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;

export type GetPaymentMethodsWalletQuery = {
  __typename?: 'Query';
  getPaymentMethodsWallet?: Array<{
    __typename?: 'PaymentMethod';
    paymentMethodId?: string | null;
    lastFour?: string | null;
    cardType?: string | null;
    default?: boolean | null;
    deleteDate?: string | null;
  } | null> | null;
};

export const GetPaymentMethodsWalletDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: { kind: 'Name', value: 'getPaymentMethodsWallet' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'getPaymentMethodsWallet' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'userId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'userId' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'paymentMethodId' },
                },
                { kind: 'Field', name: { kind: 'Name', value: 'lastFour' } },
                { kind: 'Field', name: { kind: 'Name', value: 'cardType' } },
                { kind: 'Field', name: { kind: 'Name', value: 'default' } },
                { kind: 'Field', name: { kind: 'Name', value: 'deleteDate' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetPaymentMethodsWalletQuery,
  GetPaymentMethodsWalletQueryVariables
>;
