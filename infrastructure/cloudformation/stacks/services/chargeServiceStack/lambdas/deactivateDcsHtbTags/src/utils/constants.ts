export enum TAG_NOTES {
    DCS = 'VIRTUAL-DCS',
    HTB = 'VIRTUAL-HTB',
    RFID = 'PHYSICAL-RFID',
}

export enum Countries {
    NL = 'NL',
    DE = 'DE',
    ES = 'ES',
}

export enum ReasonForBlocking {
    RequestToCancel = 'Request to Cancel',
}

export enum TagStatus {
    TERMINATED = 'TERMINATED',
}

export interface TerminateDCSContractInput {
    dcsContractId: string;
    country: string;
}

export interface DeactivateHTBCardInput {
    serialNumber: string;
    cardNumber: string;
    country: string;
}

export interface UpdateTagInternalInput {
    salesforceId: string;
    tagCardNumber: string;
    country: string;
    tagBarredDatetime: string;
    tagStatus: string;
}

export interface BlockRFIDInput {
    userId: string;
    country: string;
    reasonForBlocking: string;
    cardUid: string;
    cardNumber: string;
}

export interface QueryDefaultResponse {
    status: number;
    message: string;
}
