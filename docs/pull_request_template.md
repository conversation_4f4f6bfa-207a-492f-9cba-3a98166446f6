<b>Please review the [PR notes](https://dev.azure.com/bp-digital/bp_pulse/_git/charge-master-mobile-backend?path=/docs/pull-request-notes.md&_a=preview) if you are not certain of the below before merging the PR. Thanks :smile: </b>

## :warning: Risks :warning:

<!--- PLEASE FILL IN IF ANY OF THE BELOW APPLY -->

- [ ] Change that requires knex database migration affecting existing tables
- [ ] Change that affects user server queries
- [ ] Change that cannot be properly tested in lower envs

## :rocket: Checklist:

<!--- Go over all the following points, and put an `x` in all the boxes that apply for the service(s)/infrastructure you have changed. -->

- [ ] I have added/updated unit tests and relevant contract tests to cover my changes.
- [ ] I have updated documentation/API specs in the **insomnia** folder
- [ ] I have added/updated env vars in the correct places where necessary for all envs ([including ADO library](https://dev.azure.com/bp-digital/bp_pulse/_library?itemType=VariableGroups))
- [ ] I have added test provider mocks if applicable
- [ ] I have tested my changes do not break existing functionality for all applicable user types
- [ ] If I'm adding a new dependency, it has been evaluated by a committee ([see guidance](https://basproducts.atlassian.net/wiki/spaces/bppulse/pages/**********/Dependency+Management))
- [ ] If I have identified a risk above, I understand the steps I need to follow to mitigate the impact of this

## :pencil: PR Description

<!--- If you make a breaking change, update the PR title to use ! decorator (e.g. chore!:), check the box and describe the scope below -->

- [ ] :firecracker: BREAKING CHANGE -

<!--- Give a brief description of your changes -->
