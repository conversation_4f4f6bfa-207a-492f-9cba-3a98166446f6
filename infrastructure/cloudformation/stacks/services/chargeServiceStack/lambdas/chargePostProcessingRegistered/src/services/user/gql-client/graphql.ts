/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
};

export type AddRoamingResponse = {
  __typename?: 'AddRoamingResponse';
  dcsContractId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type AddressDetails = {
  addressCity?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLine?: InputMaybe<Scalars['String']['input']>;
  addressPostcode?: InputMaybe<Scalars['String']['input']>;
};

export type AllUpdatedTagResponse = {
  __typename?: 'AllUpdatedTagResponse';
  country?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
  tagBarredDatetime?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryName?: Maybe<Scalars['String']['output']>;
  tagDeletedFlag?: Maybe<Scalars['String']['output']>;
  tagExpiredDatetime?: Maybe<Scalars['String']['output']>;
  tagLastUsedDatetime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagSerialNumber?: Maybe<Scalars['String']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeName?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userStatus?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type BillingAmount = {
  __typename?: 'BillingAmount';
  amount?: Maybe<Scalars['Float']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export enum BillingCycle {
  MONTHLY = 'MONTHLY',
}

export type CreateSubsPlanInput = {
  billingAmount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  default?: InputMaybe<Scalars['Boolean']['input']>;
  description: Scalars['String']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  planType?: InputMaybe<PlanType>;
  scheme_id?: InputMaybe<Scalars['Int']['input']>;
};

export type DeleteAccountRequestInput = {
  appCountry: Scalars['String']['input'];
  balance: Scalars['Float']['input'];
  deletionOption?: InputMaybe<DeletionType>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  isDeleteConfirmed: Scalars['Boolean']['input'];
  lastName: Scalars['String']['input'];
  reason: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
  userType: Scalars['String']['input'];
};

export type DeleteAccountResponse = {
  __typename?: 'DeleteAccountResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type DeletePartnerTokenResponse = {
  __typename?: 'DeletePartnerTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type DeleteUserRespnse = {
  __typename?: 'DeleteUserRespnse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  user?: Maybe<User>;
};

export enum DeletionType {
  ACCOUNTS_AND_DATA = 'ACCOUNTS_AND_DATA',
  ALL_ACCOUNTS = 'ALL_ACCOUNTS',
  PULSE_DATA = 'PULSE_DATA',
}

export type Entitlements = {
  __typename?: 'Entitlements';
  chargepointsAvailable?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  originEntitlementID?: Maybe<Scalars['String']['output']>;
  partnerSchemes?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  paymentMethods?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidDefaultProviders?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidEnabled?: Maybe<Scalars['Boolean']['output']>;
  subsEnabled?: Maybe<Scalars['Boolean']['output']>;
};

export type GenericBpcmResponse = {
  __typename?: 'GenericBPCMResponse';
  payload?: Maybe<GenericBpcmResponsePayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type GenericBpcmResponsePayload = {
  __typename?: 'GenericBPCMResponsePayload';
  code?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type GetMarketingPreferenceResponse = {
  __typename?: 'GetMarketingPreferenceResponse';
  generalMarketing?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  personalMarketing?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GoCardless = {
  __typename?: 'GoCardless';
  mandateId?: Maybe<Scalars['String']['output']>;
  mandateStatus?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type GoPasswordlessResponse = {
  __typename?: 'GoPasswordlessResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GuestEntitlement = {
  __typename?: 'GuestEntitlement';
  chargepointsAvailable?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  paymentMethods?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  rfidEnabled?: Maybe<Scalars['Boolean']['output']>;
  subsEnabled?: Maybe<Scalars['Boolean']['output']>;
};

export type MarketingConsents = {
  generalMarketing?: InputMaybe<Scalars['Boolean']['input']>;
  personalMarketing?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Membership = {
  __typename?: 'Membership';
  membershipBillingCycleDate?: Maybe<Scalars['String']['output']>;
  membershipEndDate?: Maybe<Scalars['String']['output']>;
  membershipExternalId?: Maybe<Scalars['String']['output']>;
  membershipId?: Maybe<Scalars['String']['output']>;
  membershipRequestCancelDate?: Maybe<Scalars['String']['output']>;
  membershipStartDate?: Maybe<Scalars['String']['output']>;
  membershipStatus?: Maybe<Scalars['String']['output']>;
  membershipTier?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type MembershipInternalResponse = {
  __typename?: 'MembershipInternalResponse';
  membership?: Maybe<Membership>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export enum MigrationStatus {
  MANDATORY = 'MANDATORY',
  OPTIONAL = 'OPTIONAL',
}

export type Mutation = {
  __typename?: 'Mutation';
  addRoaming?: Maybe<AddRoamingResponse>;
  cancelSubscription?: Maybe<GenericBpcmResponse>;
  createMembershipInternal?: Maybe<MembershipInternalResponse>;
  createSubsPlanInternal?: Maybe<PlanResponse>;
  deleteAccountRequest?: Maybe<DeleteAccountResponse>;
  deletePartnerToken?: Maybe<DeletePartnerTokenResponse>;
  deleteUserInternal?: Maybe<DeleteUserRespnse>;
  goPasswordlessUserStageOne?: Maybe<GoPasswordlessResponse>;
  goPasswordlessUserStageTwo?: Maybe<GoPasswordlessResponse>;
  notifyRegistration?: Maybe<RegistrationResponse>;
  onboardAccount?: Maybe<OnboardAccountResponse>;
  onboardCharging?: Maybe<OnboardChargingResponse>;
  onboardPartner?: Maybe<OnboardPartnerResponse>;
  onboardRoaming?: Maybe<OnboardRoamingResponse>;
  onboardUberInternal?: Maybe<OnboardUberInternalResponse>;
  removePartnerDriverStatus?: Maybe<RemovePartnerDriverStatusResponse>;
  unblockPartnerToken?: Maybe<UnblockPartnerTokenResponse>;
  updateAccountStatus?: Maybe<UpdateAccountStatusResponse>;
  updateMarketingPreference?: Maybe<UpdateMarketingPreferenceResponse>;
  updateMembershipInternal?: Maybe<MembershipInternalResponse>;
  updateSubsPlanInternal?: Maybe<UpdatedSubsPlanResponse>;
  updateSubscriptionPreference?: Maybe<GenericBpcmResponse>;
  updateTagInternal?: Maybe<UpdateInternalResponse>;
  updateTagSchemeInternal?: Maybe<UpdateTagSchemeInternalResponse>;
  updateUserInternal?: Maybe<UpdateInternalResponse>;
  updateUserSchemeInternal?: Maybe<UpdateUserSchemeInternalResponse>;
  upsertPartnerToken?: Maybe<UpsertPartnerTokenResponse>;
  upsertRFIDTagProviderInternal?: Maybe<UpdateInternalResponse>;
  verifyPartnerToken: VerifyPartnerTokenResponse;
};

export type MutationAddRoamingArgs = {
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationCancelSubscriptionArgs = {
  userId: Scalars['String']['input'];
};

export type MutationCreateMembershipInternalArgs = {
  billingCycleDate?: InputMaybe<Scalars['String']['input']>;
  membershipExternalId?: InputMaybe<Scalars['String']['input']>;
  membershipTier?: InputMaybe<Scalars['String']['input']>;
  membershipUpgrade: Scalars['Boolean']['input'];
  partner?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationCreateSubsPlanInternalArgs = {
  payload: CreateSubsPlanInput;
};

export type MutationDeleteAccountRequestArgs = {
  payload?: InputMaybe<DeleteAccountRequestInput>;
};

export type MutationDeletePartnerTokenArgs = {
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationDeleteUserInternalArgs = {
  softDelete?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationGoPasswordlessUserStageOneArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  phoneCountryCode: Scalars['String']['input'];
  phoneNumber: Scalars['String']['input'];
  userAgent?: InputMaybe<Scalars['String']['input']>;
};

export type MutationGoPasswordlessUserStageTwoArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  code: Scalars['String']['input'];
};

export type MutationNotifyRegistrationArgs = {
  appCountry: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationOnboardAccountArgs = {
  appCountry?: InputMaybe<SupportedCountry>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  homeCountry?: InputMaybe<SupportedCountry>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  migratedUser?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationOnboardChargingArgs = {
  appCountry?: InputMaybe<SupportedCountry>;
  email?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationOnboardPartnerArgs = {
  membershipId?: InputMaybe<Scalars['String']['input']>;
  partnerType: SupportedPartner;
  userId: Scalars['String']['input'];
};

export type MutationOnboardRoamingArgs = {
  appCountry?: InputMaybe<SupportedCountry>;
  userId: Scalars['String']['input'];
};

export type MutationOnboardUberInternalArgs = {
  userId: Scalars['String']['input'];
};

export type MutationRemovePartnerDriverStatusArgs = {
  partnerType: SupportedPartner;
  userId: Scalars['String']['input'];
};

export type MutationUnblockPartnerTokenArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationUpdateAccountStatusArgs = {
  isBlock: Scalars['Boolean']['input'];
  userIds: Array<Scalars['String']['input']>;
};

export type MutationUpdateMarketingPreferenceArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  consents: MarketingConsents;
  userAgent: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationUpdateMembershipInternalArgs = {
  billingCycleDate?: InputMaybe<Scalars['String']['input']>;
  membershipCancelRequested: Scalars['Boolean']['input'];
  membershipId: Scalars['String']['input'];
  membershipStatus?: InputMaybe<Scalars['String']['input']>;
  membershipTier?: InputMaybe<Scalars['String']['input']>;
  subsFlag?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationUpdateSubsPlanInternalArgs = {
  payload?: InputMaybe<UpdateSubsPlanPayload>;
};

export type MutationUpdateSubscriptionPreferenceArgs = {
  addressDetails?: InputMaybe<AddressDetails>;
  cardPreference: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationUpdateTagInternalArgs = {
  payload: UpdateTagInternalInput;
};

export type MutationUpdateTagSchemeInternalArgs = {
  schemeName: Scalars['String']['input'];
  tagCardNumbers: Array<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationUpdateUserInternalArgs = {
  payload: UpdateUserInternalInput;
};

export type MutationUpdateUserSchemeInternalArgs = {
  schemeName: SupportedUserScheme;
  userId: Scalars['String']['input'];
};

export type MutationUpsertPartnerTokenArgs = {
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<TokenStatus>;
  token: Scalars['String']['input'];
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationUpsertRfidTagProviderInternalArgs = {
  payload: UpsertRfidTagProviderInternalInput;
};

export type MutationVerifyPartnerTokenArgs = {
  token: Scalars['String']['input'];
  tokenType: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type OnboardAccountResponse = {
  __typename?: 'OnboardAccountResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardChargingResponse = {
  __typename?: 'OnboardChargingResponse';
  message: Scalars['String']['output'];
  providers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardPartnerResponse = {
  __typename?: 'OnboardPartnerResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardRoamingResponse = {
  __typename?: 'OnboardRoamingResponse';
  message: Scalars['String']['output'];
  providers?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardUberInternalResponse = {
  __typename?: 'OnboardUberInternalResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type OnboardingStatus = {
  __typename?: 'OnboardingStatus';
  account?: Maybe<Scalars['Boolean']['output']>;
  charging?: Maybe<Scalars['Boolean']['output']>;
  country?: Maybe<SupportedCountry>;
  partners?: Maybe<Array<Maybe<Partner>>>;
  providers?: Maybe<Array<Maybe<ProviderWithDateCreated>>>;
  roaming?: Maybe<Scalars['Boolean']['output']>;
};

export type Partner = {
  __typename?: 'Partner';
  partner?: Maybe<SupportedPartner>;
};

export type PartnerTariffs = {
  __typename?: 'PartnerTariffs';
  externalMembershipId?: Maybe<Scalars['String']['output']>;
  originTypeId?: Maybe<Scalars['Int']['output']>;
  partnerType?: Maybe<SupportedPartner>;
  schemeName?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['String']['output']>;
  userAuthenticationId?: Maybe<Scalars['String']['output']>;
};

export type PartnerTariffsUberUk = {
  __typename?: 'PartnerTariffsUberUK';
  originTypeId?: Maybe<Scalars['Int']['output']>;
  partnerType?: Maybe<SupportedPartner>;
  token?: Maybe<Scalars['String']['output']>;
  userAuthenticationId?: Maybe<Scalars['String']['output']>;
};

export enum PaymentAuthType {
  MIT = 'MIT',
  PREAUTH = 'PREAUTH',
}

export type Plan = {
  __typename?: 'Plan';
  billingAmount?: Maybe<BillingAmount>;
  externalPlanId?: Maybe<Scalars['String']['output']>;
  planDefault?: Maybe<Scalars['Boolean']['output']>;
  planDuration?: Maybe<Scalars['Int']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
  planid?: Maybe<Scalars['String']['output']>;
};

export type PlanResponse = {
  __typename?: 'PlanResponse';
  externalPlanId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type PlanResults = {
  __typename?: 'PlanResults';
  plans?: Maybe<Array<Maybe<Plan>>>;
};

export enum PlanType {
  BASE = 'BASE',
  OFFER = 'OFFER',
}

export type ProviderWithDateCreated = {
  __typename?: 'ProviderWithDateCreated';
  created?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
};

export enum Providers {
  BPCM = 'BPCM',
  CHARGEVISION = 'CHARGEVISION',
  DCS = 'DCS',
  HASTOBE = 'HASTOBE',
  HTB = 'HTB',
  USOCPI = 'USOCPI',
}

export type Query = {
  __typename?: 'Query';
  getAllActiveTagsByProvider?: Maybe<Array<Maybe<TagRecord>>>;
  getAllPartnerTariffsInternal?: Maybe<Array<Maybe<PartnerTariffs>>>;
  getAllUpdatedTagData?: Maybe<Array<Maybe<AllUpdatedTagResponse>>>;
  getGuestEntitlement?: Maybe<GuestEntitlement>;
  getMarketingPreference?: Maybe<GetMarketingPreferenceResponse>;
  getPendingSubsMemberships?: Maybe<Array<Maybe<Membership>>>;
  getPendingTagProvidersByCountry?: Maybe<Array<Maybe<TagWithProviders>>>;
  getSubsOfferPlan?: Maybe<Plan>;
  getSubsPlan?: Maybe<PlanResults>;
  getSubscriptionCredit?: Maybe<SubscriptionCredit>;
  getSubscriptionPreference?: Maybe<SubscriptionPreference>;
  getUberUKPartnerTariffsInternal?: Maybe<Array<Maybe<PartnerTariffsUberUk>>>;
  onboardingStatus?: Maybe<OnboardingStatus>;
  tagInfo?: Maybe<Tags>;
  userInfo?: Maybe<UserInfo>;
};

export type QueryGetAllActiveTagsByProviderArgs = {
  provider?: InputMaybe<Scalars['String']['input']>;
};

export type QueryGetAllPartnerTariffsInternalArgs = {
  partners?: InputMaybe<Array<InputMaybe<SupportedPartner>>>;
};

export type QueryGetAllUpdatedTagDataArgs = {
  country: Scalars['String']['input'];
  tagLastUsedFrom: Scalars['String']['input'];
  tagLastUsedTo: Scalars['String']['input'];
};

export type QueryGetGuestEntitlementArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
};

export type QueryGetMarketingPreferenceArgs = {
  accessToken: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  userAgent: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type QueryGetPendingSubsMembershipsArgs = {
  country?: InputMaybe<SupportedCountry>;
  onDate: Array<InputMaybe<Scalars['String']['input']>>;
};

export type QueryGetPendingTagProvidersByCountryArgs = {
  country: Scalars['String']['input'];
  limit: Scalars['Int']['input'];
  offset: Scalars['Int']['input'];
};

export type QueryGetSubsOfferPlanArgs = {
  country: Scalars['String']['input'];
  subsFilter?: InputMaybe<SubsFilterInput>;
};

export type QueryGetSubsPlanArgs = {
  externalPlanIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  userId: Scalars['String']['input'];
};

export type QueryGetSubscriptionCreditArgs = {
  userId: Scalars['String']['input'];
};

export type QueryGetSubscriptionPreferenceArgs = {
  userId: Scalars['String']['input'];
};

export type QueryOnboardingStatusArgs = {
  appCountry?: InputMaybe<SupportedCountry>;
  userId: Scalars['String']['input'];
};

export type QueryTagInfoArgs = {
  country: Scalars['String']['input'];
  tagCardNumber: Scalars['String']['input'];
  tagSerialNumber?: InputMaybe<Scalars['String']['input']>;
};

export type QueryUserInfoArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type RegistrationResponse = {
  __typename?: 'RegistrationResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type RemovePartnerDriverStatusResponse = {
  __typename?: 'RemovePartnerDriverStatusResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type RevenuePlans = {
  __typename?: 'RevenuePlans';
  provider?: Maybe<Scalars['String']['output']>;
  revenuePlanDescription?: Maybe<Scalars['String']['output']>;
  revenuePlanName?: Maybe<Scalars['String']['output']>;
};

export type Schemes = {
  __typename?: 'Schemes';
  schemeId?: Maybe<Scalars['Float']['output']>;
  schemeName?: Maybe<Scalars['String']['output']>;
};

export type SubsFilterInput = {
  subsDiscount: Scalars['Float']['input'];
  subsDuration: Scalars['Int']['input'];
};

export type SubscriptionCredit = {
  __typename?: 'SubscriptionCredit';
  payload?: Maybe<SubscriptionCreditPayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionCreditData = {
  __typename?: 'SubscriptionCreditData';
  currency?: Maybe<Scalars['String']['output']>;
  invoiceCredit?: Maybe<Scalars['Float']['output']>;
  tagPlanCredit?: Maybe<Scalars['Float']['output']>;
  tagPlanCreditTotal?: Maybe<Scalars['Float']['output']>;
};

export type SubscriptionCreditPayload = {
  __typename?: 'SubscriptionCreditPayload';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<SubscriptionCreditData>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionData = {
  __typename?: 'SubscriptionData';
  /** @deprecated Field no longer supported */
  addressCity?: Maybe<Scalars['String']['output']>;
  /** @deprecated Field no longer supported */
  addressCountry?: Maybe<Scalars['String']['output']>;
  /** @deprecated Field no longer supported */
  addressLine?: Maybe<Scalars['String']['output']>;
  /** @deprecated Field no longer supported */
  addressPostcode?: Maybe<Scalars['String']['output']>;
  cardPreference?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  toBeCancelled?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionPlan = {
  __typename?: 'SubscriptionPlan';
  externalPlanId?: Maybe<Scalars['String']['output']>;
  subsPlanCost?: Maybe<Scalars['String']['output']>;
  subsPlanCreatedDate?: Maybe<Scalars['String']['output']>;
  subsPlanCurrency?: Maybe<Scalars['String']['output']>;
  subsPlanDefault?: Maybe<Scalars['Boolean']['output']>;
  subsPlanDeletionDate?: Maybe<Scalars['String']['output']>;
  subsPlanDescription?: Maybe<Scalars['String']['output']>;
  subsPlanDuration?: Maybe<Scalars['Int']['output']>;
  subsPlanName?: Maybe<Scalars['String']['output']>;
  subsPlanUpdatedDate?: Maybe<Scalars['String']['output']>;
  subscriptionPlanId?: Maybe<Scalars['Int']['output']>;
};

export type SubscriptionPreference = {
  __typename?: 'SubscriptionPreference';
  payload?: Maybe<SubscriptionPreferencePayload>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionPreferencePayload = {
  __typename?: 'SubscriptionPreferencePayload';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<SubscriptionData>;
  error?: Maybe<Scalars['Boolean']['output']>;
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
};

export enum SupportedCountry {
  AU = 'AU',
  DE = 'DE',
  ES = 'ES',
  NL = 'NL',
  NZ = 'NZ',
  UK = 'UK',
  UK_Beta = 'UK_Beta',
  UK_CV = 'UK_CV',
  US = 'US',
}

export enum SupportedPartner {
  ADAC = 'ADAC',
  Uber = 'Uber',
  Uber_EVC = 'Uber_EVC',
}

export enum SupportedUserScheme {
  ADAC_DE_SCHEME = 'ADAC_DE_SCHEME',
  DEFAULT_DE_EUROPEAN_SCHEME = 'DEFAULT_DE_EUROPEAN_SCHEME',
  DEFAULT_ES_EUROPEAN_SCHEME = 'DEFAULT_ES_EUROPEAN_SCHEME',
  DEFAULT_ES_SCHEME = 'DEFAULT_ES_SCHEME',
  DEFAULT_NL_EUROPEAN_SCHEME = 'DEFAULT_NL_EUROPEAN_SCHEME',
  DE_FREE_SUBSCRIPTION = 'DE_FREE_SUBSCRIPTION',
  NL_FREE_SUBSCRIPTION = 'NL_FREE_SUBSCRIPTION',
  UBER_PRO_NL_BLUE_TIER = 'UBER_PRO_NL_BLUE_TIER',
  UBER_PRO_NL_DIAMOND_TIER = 'UBER_PRO_NL_DIAMOND_TIER',
  UBER_PRO_NL_GOLD_TIER = 'UBER_PRO_NL_GOLD_TIER',
  UBER_PRO_NL_PLATINUM_TIER = 'UBER_PRO_NL_PLATINUM_TIER',
  UK_DEFAULT_SCHEME = 'UK_DEFAULT_SCHEME',
  UK_SUBSCRIPTION_SCHEME = 'UK_SUBSCRIPTION_SCHEME',
}

export enum TagProviderStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  TERMINATED = 'TERMINATED',
}

export type TagRecord = {
  __typename?: 'TagRecord';
  engineerTagFlag?: Maybe<Scalars['String']['output']>;
  tagBarredDateTime?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryId?: Maybe<Scalars['Float']['output']>;
  tagCreatedDateTime?: Maybe<Scalars['String']['output']>;
  tagDeletedDateTime?: Maybe<Scalars['String']['output']>;
  tagDeletedFlag?: Maybe<Scalars['String']['output']>;
  tagExpiresDateTime?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  tagLastUsedDateTime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagSerialNumber?: Maybe<Scalars['String']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeId?: Maybe<Scalars['Float']['output']>;
  tagVerifiedFlag?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type TagWithProviders = {
  __typename?: 'TagWithProviders';
  country?: Maybe<Scalars['String']['output']>;
  missingProviders?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCreatedDateTime?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  tagInternalId?: Maybe<Scalars['Int']['output']>;
  tagLastUsedDateTime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagProviderId?: Maybe<Scalars['Int']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Tags = {
  __typename?: 'Tags';
  country?: Maybe<Scalars['String']['output']>;
  tagCardNumber?: Maybe<Scalars['String']['output']>;
  tagCategoryName?: Maybe<Scalars['String']['output']>;
  tagCreatedDateTime?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  tagInternalId?: Maybe<Scalars['Int']['output']>;
  tagLastUsedDateTime?: Maybe<Scalars['String']['output']>;
  tagNotes?: Maybe<Scalars['String']['output']>;
  tagPartner?: Maybe<Scalars['Boolean']['output']>;
  tagStatus?: Maybe<Scalars['String']['output']>;
  tagTypeName?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Token = {
  __typename?: 'Token';
  expiryDate?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  tokenType?: Maybe<Scalars['String']['output']>;
};

export enum TokenStatus {
  INVALID = 'INVALID',
  REAUTH = 'REAUTH',
  VALID = 'VALID',
}

export type UberTier = {
  __typename?: 'UberTier';
  display_name?: Maybe<Scalars['String']['output']>;
  tier?: Maybe<Scalars['String']['output']>;
};

export type UnblockPartnerTokenResponse = {
  __typename?: 'UnblockPartnerTokenResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type UpdateAccountStatusResponse = {
  __typename?: 'UpdateAccountStatusResponse';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  userIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type UpdateInternalResponse = {
  __typename?: 'UpdateInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateMarketingPreferenceResponse = {
  __typename?: 'UpdateMarketingPreferenceResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateRevenuePlanInternalInput = {
  chargeTypeId?: InputMaybe<Scalars['String']['input']>;
  revenuePlanDeletedDate?: InputMaybe<Scalars['String']['input']>;
  revenuePlanDescription?: InputMaybe<Scalars['String']['input']>;
  revenuePlanEffectiveDate?: InputMaybe<Scalars['String']['input']>;
  revenuePlanEnergyCharge?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanFreeMonths?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanId: Scalars['String']['input'];
  revenuePlanName?: InputMaybe<Scalars['String']['input']>;
  revenuePlanTimeCharge?: InputMaybe<Scalars['Float']['input']>;
  revenuePlanUpdatedDate?: InputMaybe<Scalars['String']['input']>;
  revenueRateCost?: InputMaybe<Scalars['Float']['input']>;
  schemeId: Scalars['String']['input'];
};

export type UpdateRevenuePlanInternalResponse = {
  __typename?: 'UpdateRevenuePlanInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateSubsPlanInternalResponse = {
  __typename?: 'UpdateSubsPlanInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdateSubsPlanPayload = {
  billingAmount: Scalars['Float']['input'];
  country?: InputMaybe<Scalars['String']['input']>;
  currency: Scalars['String']['input'];
  default?: InputMaybe<Scalars['Boolean']['input']>;
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  subscriptionPlanId: Scalars['String']['input'];
};

export type UpdateTagInternalInput = {
  country: Scalars['String']['input'];
  salesforceId: Scalars['String']['input'];
  tagBarredDatetime?: InputMaybe<Scalars['String']['input']>;
  tagCardNumber: Scalars['String']['input'];
  tagCategoryName?: InputMaybe<Scalars['String']['input']>;
  tagDeletedFlag?: InputMaybe<Scalars['String']['input']>;
  tagExpiresDatetime?: InputMaybe<Scalars['String']['input']>;
  tagLastUsedDatetime?: InputMaybe<Scalars['String']['input']>;
  tagNotes?: InputMaybe<Scalars['String']['input']>;
  tagPartner?: InputMaybe<Scalars['Boolean']['input']>;
  tagSerialNumber?: InputMaybe<Scalars['String']['input']>;
  tagStatus?: InputMaybe<Scalars['String']['input']>;
  tagTypeName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateTagSchemeInternalResponse = {
  __typename?: 'UpdateTagSchemeInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type UpdateUserInternalInput = {
  country: Scalars['String']['input'];
  userBalance?: InputMaybe<Scalars['Float']['input']>;
  userBalanceCurrency?: InputMaybe<Scalars['String']['input']>;
  userCancelledDateTime?: InputMaybe<Scalars['String']['input']>;
  userCancelledReason?: InputMaybe<Scalars['String']['input']>;
  userDeletedDateTime?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
  userNotes?: InputMaybe<Scalars['String']['input']>;
  userStatus?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserSchemeInternalResponse = {
  __typename?: 'UpdateUserSchemeInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UpdatedSubsPlanResponse = {
  __typename?: 'UpdatedSubsPlanResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  billingAmount?: Maybe<BillingAmount>;
  billingCycle?: Maybe<BillingCycle>;
  billingCycleCount?: Maybe<Scalars['Int']['output']>;
  billingFrequency?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
  planType?: Maybe<PlanType>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpsertPartnerTokenResponse = {
  __typename?: 'UpsertPartnerTokenResponse';
  message?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<UberTier>;
  status?: Maybe<Scalars['String']['output']>;
};

export type User = {
  __typename?: 'User';
  addressId?: Maybe<Scalars['String']['output']>;
  authenticationId?: Maybe<Scalars['String']['output']>;
  balance?: Maybe<Scalars['String']['output']>;
  balanceCurrency?: Maybe<Scalars['String']['output']>;
  cancelledDatetime?: Maybe<Scalars['String']['output']>;
  cancelledReason?: Maybe<Scalars['String']['output']>;
  cardPreference?: Maybe<Scalars['String']['output']>;
  createdDatetime?: Maybe<Scalars['String']['output']>;
  deletedDatetime?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  language?: Maybe<Scalars['String']['output']>;
  notes?: Maybe<Scalars['String']['output']>;
  originTypeId?: Maybe<Scalars['Int']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  roamingEnabled?: Maybe<Scalars['Boolean']['output']>;
  salesforceId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  typeId?: Maybe<Scalars['String']['output']>;
};

export type UserInfo = {
  __typename?: 'UserInfo';
  balance?: Maybe<Scalars['Float']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  entitlements?: Maybe<Entitlements>;
  gocardless?: Maybe<GoCardless>;
  membership?: Maybe<Array<Maybe<Membership>>>;
  message?: Maybe<Scalars['String']['output']>;
  migrationUserStatus?: Maybe<MigrationStatus>;
  partnerType?: Maybe<Scalars['String']['output']>;
  paymentAuthType?: Maybe<PaymentAuthType>;
  /** @deprecated replaced by revenuePlans */
  revenuePlanNames?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  revenuePlans?: Maybe<Array<Maybe<RevenuePlans>>>;
  roamingEnabled?: Maybe<Scalars['Boolean']['output']>;
  schemes?: Maybe<Array<Maybe<Schemes>>>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionPlan?: Maybe<SubscriptionPlan>;
  tagIds?: Maybe<Array<Maybe<Tags>>>;
  tokens?: Maybe<Array<Maybe<Token>>>;
  type?: Maybe<Scalars['String']['output']>;
  userCancelledDateTime?: Maybe<Scalars['String']['output']>;
  userMigrationDate?: Maybe<Scalars['String']['output']>;
  userStatus?: Maybe<Scalars['String']['output']>;
};

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  DELETED = 'DELETED',
}

export type VerifyPartnerTokenResponse = {
  __typename?: 'VerifyPartnerTokenResponse';
  message: Scalars['String']['output'];
  plan?: Maybe<UberTier>;
  status: Scalars['Int']['output'];
};

export type UpsertRfidTagProviderInternalInput = {
  providers: Array<Providers>;
  tagId: Scalars['Int']['input'];
  tagProviderStatus: TagProviderStatus;
};

export type UserInfoQueryVariables = Exact<{
  userId: Scalars['String']['input'];
  appCountry?: InputMaybe<Scalars['String']['input']>;
}>;

export type UserInfoQuery = {
  __typename?: 'Query';
  userInfo?: {
    __typename?: 'UserInfo';
    type?: string | null;
    balance?: number | null;
    country?: string | null;
    paymentAuthType?: PaymentAuthType | null;
    tagIds?: Array<{
      __typename?: 'Tags';
      tagCardNumber?: string | null;
      tagId?: string | null;
      tagTypeName?: string | null;
      tagStatus?: string | null;
      tagNotes?: string | null;
    } | null> | null;
  } | null;
};

export type UpdateUserInternalMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  country: Scalars['String']['input'];
  userBalance?: InputMaybe<Scalars['Float']['input']>;
}>;

export type UpdateUserInternalMutation = {
  __typename?: 'Mutation';
  updateUserInternal?: {
    __typename?: 'UpdateInternalResponse';
    status?: string | null;
    message?: string | null;
  } | null;
};

export const UserInfoDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: { kind: 'Name', value: 'userInfo' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'appCountry' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'userInfo' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'userId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'userId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'appCountry' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'appCountry' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                { kind: 'Field', name: { kind: 'Name', value: 'type' } },
                { kind: 'Field', name: { kind: 'Name', value: 'balance' } },
                { kind: 'Field', name: { kind: 'Name', value: 'country' } },
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'paymentAuthType' },
                },
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'tagIds' },
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'tagCardNumber' },
                      },
                      { kind: 'Field', name: { kind: 'Name', value: 'tagId' } },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'tagTypeName' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'tagStatus' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'tagNotes' },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<UserInfoQuery, UserInfoQueryVariables>;
export const UpdateUserInternalDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: { kind: 'Name', value: 'updateUserInternal' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'country' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userBalance' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'Float' } },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'updateUserInternal' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'payload' },
                value: {
                  kind: 'ObjectValue',
                  fields: [
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'userId' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'userId' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'country' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'country' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'userBalance' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'userBalance' },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                { kind: 'Field', name: { kind: 'Name', value: 'status' } },
                { kind: 'Field', name: { kind: 'Name', value: 'message' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateUserInternalMutation,
  UpdateUserInternalMutationVariables
>;
