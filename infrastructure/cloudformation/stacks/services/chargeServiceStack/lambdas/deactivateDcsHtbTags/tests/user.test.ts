import { getUserInfo, updateTagInternal } from '../src/services/user';
import { request } from 'graphql-request';
import { logger } from '../src/utils/logger';
import {
    GET_USER_INFO,
    UPDATE_TAG_INTERNAL,
} from '../src/services/user/queries';

jest.mock('graphql-request');
jest.mock('../src/env', () => ({
    env: require('./mocks/env.mock').env,
}));
jest.mock('../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

jest.mock('../src/services/user/queries', () => ({
    GET_USER_INFO: 'mock_get_user_info_query',
    UPDATE_TAG_INTERNAL: 'mock_update_tag_internal_query',
}));

describe('User Service', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getUserInfo', () => {
        const mockGetUserInfoInput = {
            userId: 'user-123',
            country: 'NL',
        };

        const mockUserInfoResponse = {
            userInfo: {
                tagIds: [
                    {
                        tagId: '12345',
                        tagNotes: 'virtual-DCS',
                        tagCardNumber: 'mockcardnumber1',
                        tagStatus: 'ACTIVE',
                    },
                    {
                        tagId: '67890',
                        tagNotes: 'virtual-HTB',
                        tagCardNumber: 'mockcardnumber2',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: 'NL',
                balance: 100,
            },
        };

        it('should successfully fetch user information', async () => {
            (request as jest.Mock).mockResolvedValue(mockUserInfoResponse);

            const result = await getUserInfo(mockGetUserInfoInput);

            expect(request).toHaveBeenCalledWith(
                'https://mock-private-gateway',
                GET_USER_INFO,
                {
                    userId: 'user-123',
                    appCountry: 'NL',
                },
                { 'x-apollo-internal-secret': 'mock-internal-secret' },
            );

            expect(result).toEqual(mockUserInfoResponse);
        });

        it('should handle errors when fetching user information fails', async () => {
            const mockError = new Error('GraphQL request failed');
            (request as jest.Mock).mockRejectedValue(mockError);

            await expect(getUserInfo(mockGetUserInfoInput)).rejects.toThrow(
                'message: GraphQL request failed',
            );

            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Could not get user info'),
            );
        });

        it('should throw error when country is not provided', async () => {
            await expect(
                getUserInfo({
                    userId: 'user-123',
                    country: undefined as unknown as string,
                }),
            ).rejects.toThrow();
        });
    });

    describe('updateTagInternal', () => {
        const mockUpdateTagInput = {
            salesforceId: 'user-123',
            tagCardNumber: 'CARD123',
            country: 'NL',
            tagBarredDatetime: '2023-01-01T12:00:00Z',
            tagStatus: 'TERMINATED',
        };

        const mockUpdateTagResponse = {
            status: 200,
            message: 'Tag updated successfully',
        };

        it('should successfully update a tag', async () => {
            (request as jest.Mock).mockResolvedValue({
                updateTagInternal: mockUpdateTagResponse,
            });

            const result = await updateTagInternal(mockUpdateTagInput);

            expect(request).toHaveBeenCalledWith(
                'https://mock-private-gateway',
                UPDATE_TAG_INTERNAL,
                {
                    payload: mockUpdateTagInput,
                },
                { 'x-apollo-internal-secret': 'mock-internal-secret' },
            );

            expect(result).toEqual(mockUpdateTagResponse);
        });

        it('should handle errors when updating tag fails', async () => {
            const mockError = new Error('GraphQL request failed');
            (request as jest.Mock).mockRejectedValue(mockError);

            await expect(updateTagInternal(mockUpdateTagInput)).rejects.toThrow(
                'message: GraphQL request failed',
            );

            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining(
                    'Error updating tag CARD123 for user user-123',
                ),
            );
        });

        it('should include all required fields in the update request', async () => {
            (request as jest.Mock).mockResolvedValue(mockUpdateTagResponse);

            await updateTagInternal(mockUpdateTagInput);

            const requestCall = (request as jest.Mock).mock.calls[0];
            const variables = requestCall[2];

            expect(variables.payload).toHaveProperty(
                'salesforceId',
                'user-123',
            );
            expect(variables.payload).toHaveProperty(
                'tagCardNumber',
                'CARD123',
            );
            expect(variables.payload).toHaveProperty('country', 'NL');
            expect(variables.payload).toHaveProperty(
                'tagBarredDatetime',
                '2023-01-01T12:00:00Z',
            );
            expect(variables.payload).toHaveProperty('tagStatus', 'TERMINATED');
        });
    });
});
