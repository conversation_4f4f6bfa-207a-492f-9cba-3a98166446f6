import { S3 } from 'aws-sdk';
import csv from 'csv-parser';
import { addMinutes } from 'date-fns';
import moment from 'moment-timezone';
import { Readable } from 'stream';
import { get } from '../clients/s3';
import { CDRType, Countries, DateType, PROVIDER } from './enums';
import { GenericObject, IGetInvoiceDate } from './interfaces';
const { DCS_BUCKET_NAME = 'ws-00f0-dcs-cdr-export' } = process.env;

export const computeDCSEntries = async (mappedFileNames: GenericObject) => {
  return await Promise.all(
    Object.keys(mappedFileNames).map(async (key) => {
      const fileContent: S3.Body = await get(DCS_BUCKET_NAME, key);

      const splitContent = await parseFileContents(fileContent);
      const keys = splitContent.shift() || [];

      const dcsMappedEntries = splitContent
        .map((element: any) => {
          const newDCSEntryObject: GenericObject = {};

          element.forEach((entry: string, index: number) => {
            return (newDCSEntryObject[keys[index]] = entry);
          });

          // map DCS data and combine with upper case object from CDR file
          const mappedDCSObject = {
            ...mapDCSData(
              convertObjectKeysToUpperCase({ ...newDCSEntryObject }),
            ),
            ...convertObjectKeysToUpperCase({ ...newDCSEntryObject }),
          };

          // add charge session ID to this object for easier manipulation
          const mappedDCS = mappedFileNames[key].find(
            (el: { sessionId: any; cdrType: any }) => {
              // if the session ID is prefixed with the string 'CDR_'
              // we strip the prefix and keep the rest for id comparison
              const historySessionId = el.sessionId.startsWith('CDR_')
                ? el.sessionId.substring(4)
                : el.sessionId;

              return (
                historySessionId === mappedDCSObject.sessionId &&
                matchCDRType(el.cdrType, mappedDCSObject.cdr_type)
              );
            },
          );

          const finalDCSObject = {
            ...mappedDCSObject,
            chargeSessionId: mappedDCS?.chargeSessionId,
            userId: mappedDCS?.userId,
            userType: mappedDCS?.userType,
            homeCountry: mappedDCS?.homeCountry,
            partnerType: mappedDCS?.partnerType,
            DISCOUNT: mappedDCS?.chargeDetails?.discount,
            OFFER_CODES: mappedDCS?.chargeDetails?.offerCodes
              ? JSON.stringify(mappedDCS?.chargeDetails?.offerCodes)
              : '',
            PRICE_CUST: mappedDCS?.chargeDetails?.totalGross,
            PRICE_NET_CUST: mappedDCS?.chargeDetails?.chargeNet,
            PRICE_VAT: mappedDCS?.chargeDetails?.chargeTax,
            PRICE_BASE: mappedDCS?.chargeDetails?.chargeBaseFee,
            PRICE_SURCHARGE: mappedDCS?.chargeDetails?.chargeAdditionalFees,
          };

          return finalDCSObject;
        })
        .filter((entry: any) => {
          return entry.chargeSessionId;
        });

      return dcsMappedEntries;
    }),
  );
};
export const parseFileContents = (fileContents: S3.Body): Promise<any> => {
  const stream = Readable.from(fileContents.toString());
  const dataArray: any[] = [];
  const results: any[] = [];

  return new Promise((resolve, reject) => {
    stream
      .pipe(csv({ separator: ';', escape: '"' }))
      .on('data', (data) => {
        console.info('Data from csv file...', JSON.stringify(data));
        dataArray.push(data);
      })
      .on('end', () => {
        // add the keys to the results array
        results.push(Object.keys(dataArray[0]));
        // then add the values
        dataArray.forEach((element) => results.push(Object.values(element)));
        // and return results
        resolve(results);
      })
      .on('error', () => {
        return reject('Failed to process CSV');
      });
  });
};

export const matchCDRType = (
  cdrTypeFromHistory: string,
  cdrTypeFromFile: string,
) => {
  if (cdrTypeFromHistory === cdrTypeFromFile) {
    return true;
  }
  if (
    cdrTypeFromHistory === CDRType.CHARGE &&
    cdrTypeFromFile.toLowerCase() === CDRType.CDR
  ) {
    return true;
  }
  if (
    cdrTypeFromHistory === CDRType.CREDIT &&
    cdrTypeFromFile.toLowerCase().includes(CDRType.CREDIT)
  ) {
    return true;
  }
  return false;
};

export const convertObjectKeysToUpperCase = (obj: GenericObject) => {
  return Object.keys(obj).reduce((acc: GenericObject, key) => {
    acc[key.toUpperCase()] = obj[key];
    return acc;
  }, {});
};

export const mapDCSData = ({
  SESSION_START,
  DURATION,
  UU_ID,
  CDR_EXT_ID,
  CPO_ID,
  EVSE_ID,
  CDR_ID,
  CDR_TYP,
  EVCO_ID,
  RFID_UID,
  PRICE_CURRENCY_CODE_CUST,
  PRICE_CUST,
  PRICE_NET_CUST,
  PRICE_VAT,
  PRICE_TAX_RATE,
  ENERGY,
  PRICE_BASE,
  PRICE_SURCHARGE,
}: GenericObject) => {
  const isCreditCDR = CDR_TYP.toLowerCase().includes(CDRType.CREDIT);
  return {
    transactionId: UU_ID,
    sessionId: CDR_ID,
    cdr_type: CDR_TYP,
    chargeSessionId: null,
    timestamp: {
      start: SESSION_START
        ? new Date(SESSION_START * 1000).toISOString()
        : null,
      stop: SESSION_START
        ? addMinutes(
            new Date(SESSION_START * 1000),
            parseInt(DURATION),
          ).toISOString()
        : null,
    },
    chargepoint: {
      providerExternalId: CPO_ID,
      connector: {
        connectorExternalId: EVSE_ID,
      },
    },
    chargeDetails: {
      providerChargeIds: [
        {
          type: 'sessionId',
          value: CDR_EXT_ID,
        },
        {
          type: 'transactionId',
          value: UU_ID,
        },
        {
          type: 'cdrId',
          value: CDR_ID,
        },
      ],
      tagId: RFID_UID ? RFID_UID : EVCO_ID,
      currency: PRICE_CURRENCY_CODE_CUST,
      chargeGross: PRICE_CUST,
      chargeNet: PRICE_NET_CUST,
      chargeTax: PRICE_VAT,
      chargeTaxPercentage: PRICE_TAX_RATE,
      totalGross: PRICE_CUST,
      energyConsumption: ((isCreditCDR ? -1 : 1) * parseFloat(ENERGY)).toFixed(
        3,
      ),
      unitOfMeasure: 'kWh',
      chargeBaseFee: PRICE_BASE || 0,
      chargeAdditionalFees: PRICE_SURCHARGE || 0,
    },
    BP_FLAG: CPO_ID?.slice(-3) === 'BPE' ? 'TRUE' : 'FALSE',
  };
};

export const isoDateToUnix = (isoDate: string | number) =>
  Math.floor(new Date(isoDate).getTime() / 1000).toString();

export const getCountryCode = (country: string) => {
  switch (country) {
    case Countries.UK:
      return 'GB';
    default:
      return country;
  }
};

export const isoDateToUnixLocale = (
  isoDate: string | number,
  country: string,
) => {
  const timezone = !country ? 'UTC' : getTimezone(country);

  const utcUnixDateString = isoDateToUnix(isoDate);

  const tzOffsetMins = moment.tz(timezone).utcOffset();

  const localeUnixDate = parseInt(utcUnixDateString) + tzOffsetMins * 60;

  return localeUnixDate.toString();
};

export const getTimezone = (country?: string) => {
  return moment.tz.zonesForCountry(getCountryCode(country || ''))?.[0] || 'UTC';
};

export const getTimezoneAbbr = (country?: string) => {
  const fullTimezone = getTimezone(country);
  return moment.tz(fullTimezone).zoneAbbr();
};

export const dateFormat = (
  date: string | number,
  country?: string,
  htbFormat?: boolean,
) => {
  if (!date) {
    throw new Error('date does not exist');
  }

  const timezone = !country ? 'UTC' : getTimezone(country);

  if (moment.utc(date).isValid()) {
    if (htbFormat) {
      return moment
        .utc(date)
        .tz(timezone)
        .utcOffset(0, true)
        .format()
        .replace('Z', '');
    } else {
      return moment.utc(date).tz(timezone).format();
    }
  } else {
    throw new Error('date is not valid');
  }
};

export const dateFormatUnixToISO = (
  date: string | number,
  country?: string,
) => {
  if (!date) {
    throw new Error('unix date does not exist');
  }

  date = typeof date === 'string' ? parseInt(date) : date;

  const isMsUnixDate = date.toString().length === 13;

  const convertedUnixDate = isMsUnixDate ? date : date * 1000;

  return dateFormat(convertedUnixDate, country);
};

export const addSAPFields = async (
  DCSHTBBPCMRecords: any,
  transactionReceiptDetails: any,
) => {
  for (const entry of DCSHTBBPCMRecords) {
    const details = transactionReceiptDetails.find(
      (detail: {
        charge_session_id: string;
        reference_charge_session_id: string;
        receiptDetails: any;
      }) => detail.receiptDetails.charge_session_id === entry.chargeSessionId,
    );

    let detailsTransactionDate =
      details?.[
        process.env.DATE_TYPE === DateType.SALES_POSTING
          ? 'sales_posting_date'
          : 'order_started'
      ];

    detailsTransactionDate =
      typeof detailsTransactionDate === 'string'
        ? parseInt(detailsTransactionDate)
        : detailsTransactionDate;

    const country =
      entry?.chargepoint?.country || entry.chargeSessionId.substring(2, 4);
    entry.EMSP_USER_ID = details?.user_id || entry?.userId;
    entry.EMSP_INVOICE_ID = details?.receiptDetails?.receipt_id;
    entry.EMSP_ACQUIRER = details?.receiptDetails?.acquirer;
    entry.EMSP_INVOICE_DATE = getEMSPInvoiceDate({
      cdr_type: entry.cdr_type,
      country,
      refunded_date: details?.refunded_date,
      dateEnd: entry?.timestamp?.stop,
    });
    entry.EMSP_ORDERID = details?.payment_id;
    entry.EMSP_TRANSACTION_DATE = formatTransactionDate(
      country,
      detailsTransactionDate,
    );
    entry.LOCAL_SESSION_TZ = getTimezoneAbbr(country);
    entry.RETRIEVAL_REFERENCE_NUMBER = details?.retrieval_reference_number;
    entry.EMSP_USER_TYPE = entry?.userType;

    if (entry?.chargepoint?.provider?.toUpperCase() === PROVIDER.HASTOBE) {
      entry.LOCAL_SESSION_START = dateFormat(
        entry.timestamp.start,
        country,
        true,
      );
      entry.LOCAL_SESSION_END = dateFormat(entry.timestamp.stop, country, true);
    } else {
      entry.LOCAL_SESSION_START = formatLocalSessionStart(
        entry.timestamp.start,
        country,
      );
    }
    if (entry?.chargepoint?.provider?.toUpperCase() !== PROVIDER.BPCM) {
      entry.CUSTOMER_HOME_COUNTRY = entry?.homeCountry || '';
      entry.EMSP_PARTNER_TYPE = entry?.partnerType || '';
    }
  }
  console.log('records', JSON.stringify(DCSHTBBPCMRecords));
  return DCSHTBBPCMRecords;
};

export const getEMSPInvoiceDate = ({
  cdr_type,
  country,
  refunded_date,
  dateEnd,
}: IGetInvoiceDate): string => {
  if (!refunded_date && !dateEnd) {
    console.error(
      'getEMSPInvoiceDate: refunded date and date end both missing',
    );
    // do not throw error so rest of processing can continue
    return '';
  }

  if (country === Countries.UK) {
    if (isCdrTypeCredit(cdr_type) && refunded_date) {
      return convertTimestampToISO(refunded_date).toString();
    }
    return dateEnd || '';
  }

  return isoDateToUnixLocale(
    isCdrTypeCredit(cdr_type) && refunded_date
      ? dateFormatUnixToISO(refunded_date, country)
      : dateEnd
      ? dateFormat(dateEnd, country)
      : '',
    country,
  );
};

const isCdrTypeCredit = (cdrType: string) => {
  return cdrType && cdrType.toLowerCase().includes(CDRType.CREDIT);
};

export const convertTimestampToISO = (timestamp: string | number) => {
  const numericTimestamp = Number(timestamp);

  if (isNaN(numericTimestamp)) {
    return timestamp;
  }

  const date = new Date(numericTimestamp * 1000);
  if (isNaN(date.getTime())) {
    return timestamp;
  }

  return date.toISOString();
};

export const formatTransactionDate = (country?: string, date?: number) => {
  if (!date) {
    return '';
  }

  const unixTimestampInSeconds = Math.floor(date / 1000);

  if (country === Countries.UK) {
    return convertTimestampToISO(unixTimestampInSeconds);
  }

  return unixTimestampInSeconds;
};

export const formatLocalSessionStart = (
  date: string | number,
  country: string,
) => {
  if (country === Countries.UK) {
    return date;
  }

  return isoDateToUnixLocale(dateFormat(date, country), country);
};

export const validateAndNormalizeDates = (
  startDate: string,
  endDate: string,
): { startDate: string; endDate: string } => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  //The start date must always be the beginning of a new day
  start.setUTCHours(0, 0, 0, 0);

  // Checking if end date has non-zero time components (most common manual run pitfall)
  const endHours = end.getUTCHours();
  const endMinutes = end.getUTCMinutes();
  const endSeconds = end.getUTCSeconds();

  // handling the case where end date is the same as start date but set to the
  // last second of the day - 23:59:59 - instead of next day and time  00:00:00
  if (endHours !== 0 || endMinutes !== 0 || endSeconds !== 0) {
    console.warn(
      `Invalid end date ${endDate}. ` +
        `Adjusting to next day at 00:00:00 to match expected behavior.`,
    );

    end.setUTCDate(end.getUTCDate() + 1);
    end.setUTCHours(0, 0, 0, 0);
  }

  if (end <= start) {
    throw new Error(
      `Invalid date range: endDate (${end.toISOString()}) must be after startDate (${start.toISOString()})`,
    );
  }

  const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
  if (daysDiff > 1) {
    console.log(`Large date range detected: ${daysDiff} days. `);
  }

  return {
    startDate: start.toUTCString(),
    endDate: end.toUTCString(),
  };
};
