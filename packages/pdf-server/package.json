{"name": "@bp/feature-pdf-receipt-server", "version": "0.0.1", "private": true, "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json && tsc-alias", "clean": "rm -rf dist/", "dev": "nodemon --config nodemon.json src/index.ts", "lint": "eslint . --ext .ts", "lint:fix": "npm run lint -- --fix", "start": "node -r tsconfig-paths/register -r ts-node/register ./dist/index.js", "stop": "npx kill-port 4016", "test": "jest", "test:ci": "jest --ci", "types:check": "tsc --noEmit"}, "dependencies": {"@apollo/subgraph": "^2.2.1", "@aws-sdk/client-dynamodb": "^3.454.0", "@aws-sdk/client-lambda": "^3.454.0", "@aws-sdk/client-s3": "^3.454.0", "@aws-sdk/credential-providers": "^3.454.0", "@aws-sdk/s3-request-presigner": "^3.454.0", "@aws-sdk/util-dynamodb": "^3.454.0", "apollo-server": "^3.11.1", "apollo-server-core": "^3.11.1", "apollo-server-express": "^3.11.1", "axios": "^1.10.0", "date-fns": "^2.25.0", "del": "^6.0.0", "dotenv": "^10.0.0", "exiftool-vendored": "^21.0.0", "express": "^4.17.1", "graphql": "^16.6.0", "graphql-request": "5.0.0", "moment": "^2.29.4", "schemaglue": "^4.3.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "winston": "^3.3.3", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime": "^7.15.4", "@bp/eslint-plugin": "^0.0.3", "@types/jest": "^27.0.2", "@types/xml2js": "^0.4.14", "babel-jest": "^27.3.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.20.0", "jest": "^27.3.1", "nodemon": "^2.0.12", "prettier": "2.7.1", "tsc-alias": "^1.8.4", "typescript": "^4.4.4"}, "overrides": {"nodemon": {"chokidar": {"glob-parent": "^6.0.1"}}, "eslint-import-resolver-typescript": {"globby": {"fast-glob": {"glob-parent": "^6.0.1"}}}, "@typescript-eslint/parser": {"@typescript-eslint/typescript-estree": {"globby": {"fast-glob": {"glob-parent": "^6.0.1"}}}}, "del": {"globby": {"fast-glob": {"glob-parent": "^6.0.1"}}}}}