AWSTemplateFormatVersion: 2010-09-09

Transform: AWS::Serverless-2016-10-31

Description: This template deploys charge service stack infrastructure.

Parameters:
  Environment:
    Type: String
  EnvironmentShortName:
    Type: String
  Service:
    Type: String
    Default: 'charge'
  Stack:
    Type: String
    Default: 'Charge'
  ApiVersion:
    Type: String
  StackName:
    Type: String
  BuildId:
    Type: String
  StackBucketName:
    Type: String
  AwsLogicalAccountNameUpperCase:
    Type: String
  AwsLogicalAccountNameLowerCase:
    Type: String
  LambdaSecurityGroup:
    Type: List<String>
  ExternalSubnetIds:
    Type: List<String>
  RedisNodeType:
    Type: String
  RedisCacheSubnetGroup:
    Type: String
  ElastiCacheSecurityGroups:
    Type: String
  LambdaRoleArn:
    Type: String
  ChargepointEventsKinesisStreamArn:
    Type: String
  HtbChargepointEventsKinesisStreamArn:
    Type: String
  HtbNotifyKinesisStreamArn:
    Type: String
  HtbCdrEventsKinesisStreamArn:
    Type: String
  FavouritesDbUrl:
    Type: String
  PaymentsDbRegion:
    Type: String
  PaymentsDbTableName:
    Type: String
  PrivateBppayServerHttp:
    Type: String
  InternalBppayServerHttp:
    Type: String
  HtbServicesBearerToken:
    Type: String
  HtbServicesIdentifierDE:
    Type: String
  HtbServicesIdentifierES:
    Type: String
  HtbServicesIdentifierNL:
    Type: String
  HtbServicesPasswordDE:
    Type: String
  HtbServicesIdentifierGuest:
    Type: String
  HtbServicesUsernameGuest:
    Type: String
  HtbServicesPasswordGuest:
    Type: String
  HtbXApiKey:
    Type: String
  HtbServicesPasswordNL:
    Type: String
  HtbServicesPasswordES:
    Type: String
  HtbServicesPasswordUK:
    Type: String
  HtbServicesRest:
    Type: String
  HtbServicesUrl:
    Type: String
  HtbServicesUsernameDE:
    Type: String
  HtbServicesUsernameNL:
    Type: String
  HtbServicesUsernameES:
    Type: String
  HtbServicesUsernameUK:
    Type: String
  ApiGatewayKey:
    Type: String
  ApiGatewayStreamKey:
    Type: String
  AwsServicesKey:
    Type: String
  InternalChargeServerHttp:
    Type: String
  InternalHistoryServerHttp:
    Type: String
  InternalVoucherServerHttp:
    Type: String
  PdfReceiptGatewayStreamUrl:
    Type: String
  PrivateGatewayServerHttp:
    Type: String
  ApolloInternalSecret:
    Type: String
  DcsAccountUrl:
    Type: String
  DcsSasToken:
    Type: String
  DcsDirectoryName:
    Type: String
  DcsShareName:
    Type: String
  DcsOutboundRoamingBucketName:
    Type: String
  DcsTokenClientId:
    Type: String
  DcsTokenClientSecret:
    Type: String
  DcsTokenResource:
    Type: String
  DcsTokenUrl:
    Type: String
  DcsSubscriptionKey:
    Type: String
  DcsFleetAccessKey:
    Type: String
  DcsFleetServicesUrl:
    Type: String
  NLFleetGroup:
    Type: String
  DEFleetGroup:
    Type: String
  ESFleetGroup:
    Type: String
  S3Region:
    Type: String
  DcsCdrEventsGatewayStreamUrl:
    Type: String
  NodeEnv:
    Type: String
  RegisteredChargeProcessingLambdaName:
    Type: String
  CreditCDRFeatureFlag:
    Type: String
  GuestChargeProcessingLambdaName:
    Type: String
  GoogleAnalyticsRoute:
    Type: String
  GoogleAnalyticsSecret:
    Type: String
  GoogleAnalyticsMeasurementID:
    Type: String
  PushNotificationLambdaName:
    Type: String
  ChargepointEventsKinesisShardCount:
    Type: String
  CurrentDate:
    Type: String
  ServiceVersion:
    Type: String
  ApolloClientUrl:
    Type: String
  ApolloClientKey:
    Type: String
  ChargevisionTokenEndpoint:
    Type: String
  ChargevisionUrl:
    Type: String
  ChargevisionAuthKey:
    Type: String
  DeactivateDcsHtbTagsMaxConcurrentRequests:
    Type: String

Resources:
  EventsElastiCache:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        Environment: !Ref Environment
        EnvironmentShortName: !Ref EnvironmentShortName
        Service: !Ref Service
        ApiVersion: !Ref ApiVersion
        StackName: !Ref StackName
        AwsLogicalAccountNameLowerCase: !Ref AwsLogicalAccountNameLowerCase
        RedisNodeType: !Ref RedisNodeType
        RedisCacheSubnetGroup: !Ref RedisCacheSubnetGroup
        RedisGroupDescription: 'Chargepoint Events Cache'
        RedisGroupID: chargepoint-events
        ElastiCacheSecurityGroups: !Ref ElastiCacheSecurityGroups
        CurrentDate: !Ref CurrentDate
        ServiceVersion: !Ref ServiceVersion
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/elasticache/elasticache.yaml

  ChargePostProcessingGuest:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Guest-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 60
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/chargePostProcessingGuest
      Environment:
        Variables:
          API_GATEWAY_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayKey}:SecretString:secret}}'
          API_GATEWAY_STREAM_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayStreamKey}:SecretString:secret}}'
          INTERNAL_BPPAY_SERVER_HTTP: !Ref InternalBppayServerHttp
          INTERNAL_VOUCHER_SERVER_HTTP: !Ref InternalVoucherServerHttp
          PDF_RECEIPT_GATEWAY_STREAM_URL: !Ref PdfReceiptGatewayStreamUrl
          PRIVATE_GATEWAY_SERVER_HTTP: !Ref PrivateGatewayServerHttp
          APOLLO_INTERNAL_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApolloInternalSecret}:SecretString:secret}}'
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  ChargePostProcessingRegistered:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: src/index.handler
      Timeout: 60
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/chargePostProcessingRegistered/dist
      Environment:
        Variables:
          API_GATEWAY_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayKey}:SecretString:secret}}'
          API_GATEWAY_STREAM_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayStreamKey}:SecretString:secret}}'
          INTERNAL_BPPAY_SERVER_HTTP: !Ref InternalBppayServerHttp
          INTERNAL_VOUCHER_SERVER_HTTP: !Ref InternalVoucherServerHttp
          PDF_RECEIPT_GATEWAY_STREAM_URL: !Ref PdfReceiptGatewayStreamUrl
          PRIVATE_GATEWAY_SERVER_HTTP: !Ref PrivateGatewayServerHttp
          APOLLO_INTERNAL_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApolloInternalSecret}:SecretString:secret}}'
          PUSH_NOTIFICATION_LAMBDA_NAME: !Ref PushNotificationLambdaName
          NODE_ENV: !Ref NodeEnv
          ENABLE_OFFERS: true
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  DeactivateDcsHtbTags:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Deactivate-Dcs-Htb-Tags-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 512
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: deactivateDcsHtbTags/src/index.handler
      Timeout: 900
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/deactivateDcsHtbTags/dist
      Environment:
        Variables:
          NODE_ENV: !Ref NodeEnv
          DCS_TOKEN_URL: !Ref DcsTokenUrl
          DCS_TOKEN_CLIENT_ID: !Ref DcsTokenClientId
          DCS_TOKEN_CLIENT_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${DcsTokenClientSecret}:SecretString:secret}}'
          DCS_TOKEN_RESOURCE: !Ref DcsTokenResource
          DCS_SUBSCRIPTION_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${DcsSubscriptionKey}:SecretString:secret}}'
          DCS_FLEET_ACCESS_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${DcsFleetAccessKey}:SecretString:secret}}'
          DCS_FLEET_SERVICES_URL: !Ref DcsFleetServicesUrl
          NL_FLEET_GROUP: !Ref NLFleetGroup
          DE_FLEET_GROUP: !Ref DEFleetGroup
          ES_FLEET_GROUP: !Ref ESFleetGroup
          HTB_SERVICES_USERNAME_DE: !Ref HtbServicesUsernameDE
          HTB_SERVICES_PASSWORD_DE: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordDE}:SecretString:secret}}'
          HTB_SERVICES_USERNAME_ES: !Ref HtbServicesUsernameES
          HTB_SERVICES_PASSWORD_ES: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordES}:SecretString:secret}}'
          HTB_SERVICES_USERNAME_NL: !Ref HtbServicesUsernameNL
          HTB_SERVICES_PASSWORD_NL: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordNL}:SecretString:secret}}'
          HTB_X_API_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbXApiKey}:SecretString:secret}}'
          HTB_SERVICES_REST: !Ref HtbServicesRest
          HTB_SERVICES_AUTH: !Ref HtbServicesUrl
          HTB_AUTH_TOKEN: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesBearerToken}:SecretString:secret}}'
          PRIVATE_GATEWAY_SERVER_HTTP: !Ref PrivateGatewayServerHttp
          APOLLO_INTERNAL_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApolloInternalSecret}:SecretString:secret}}'
          DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS: !Ref DeactivateDcsHtbTagsMaxConcurrentRequests
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  EventsLambdaKinesis:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Chargepoint-Events-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 60
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/chargepointEvents
      Environment:
        Variables:
          ELASTICACHE_HOST: !GetAtt EventsElastiCache.Outputs.ElastiCacheEndpoint
          ELASTICACHE_PORT: !GetAtt EventsElastiCache.Outputs.ElastiCachePort
          GA_ROUTE: !Ref GoogleAnalyticsRoute
          GA_API_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${GoogleAnalyticsSecret}:SecretString:secret}}'
          GA_MEASUREMENT_ID: !Ref GoogleAnalyticsMeasurementID
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  EventsLambdaKinesisEvent:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt ChargepointEventsKinesisStream.Arn
      FunctionName: !GetAtt EventsLambdaKinesis.Arn
      StartingPosition: LATEST
      ParallelizationFactor: 2
      BatchSize: 100
      Enabled: True

  HTBEventsLambdaKinesis:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Chargepoint-Events-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs20.x
      Handler: src/index.handler
      Timeout: 30
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/htbChargeEvents/dist
      Environment:
        Variables:
          ELASTICACHE_HOST: !GetAtt EventsElastiCache.Outputs.ElastiCacheEndpoint
          ELASTICACHE_PORT: !GetAtt EventsElastiCache.Outputs.ElastiCachePort
          PRIVATE_BPPAY_SERVER_HTTP: !Ref PrivateBppayServerHttp
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  HTBEventsLambdaKinesisEvent:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt HtbChargepointEventsKinesisStream.Arn
      FunctionName: !GetAtt HTBEventsLambdaKinesis.Arn
      StartingPosition: LATEST
      BatchSize: 10
      Enabled: True

  HTBCDREventsLambdaKinesis:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-CDR-Events-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: htbCdrEvents/index.handler
      Timeout: 120
      ReservedConcurrentExecutions: 1
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/htbCdrEvents/dist
      Environment:
        Variables:
          API_GATEWAY_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayKey}:SecretString:secret}}'
          API_GATEWAY_STREAM_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayStreamKey}:SecretString:secret}}'
          AWS_SERVICES_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${AwsServicesKey}:SecretString:secret}}'
          INTERNAL_BPPAY_SERVER_HTTP: !Ref InternalBppayServerHttp
          INTERNAL_CHARGE_SERVER_HTTP: !Ref InternalChargeServerHttp
          INTERNAL_HISTORY_SERVER_HTTP: !Ref InternalHistoryServerHttp
          INTERNAL_VOUCHER_SERVER_HTTP: !Ref InternalVoucherServerHttp
          PDF_RECEIPT_GATEWAY_STREAM_URL: !Ref PdfReceiptGatewayStreamUrl
          PRIVATE_GATEWAY_SERVER_HTTP: !Ref PrivateGatewayServerHttp
          APOLLO_INTERNAL_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApolloInternalSecret}:SecretString:secret}}'
          NODE_ENV: !Ref NodeEnv
          GUEST_CHARGE_PROCESSING_LAMBDA_NAME: !Ref GuestChargeProcessingLambdaName
          REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME: !Ref RegisteredChargeProcessingLambdaName
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  HTBCDREventsLambdaKinesisEvent:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt HtbCdrEventsKinesisStream.Arn
      FunctionName: !GetAtt HTBCDREventsLambdaKinesis.Arn
      StartingPosition: LATEST
      BatchSize: 5
      Enabled: True

  DcsCdrKinesisStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub ${AwsLogicalAccountNameLowerCase}-DCS-CDR-${Environment}
      RetentionPeriodHours: 24
      ShardCount: 1
      StreamEncryption:
        EncryptionType: KMS
        KeyId: 'alias/aws/kinesis'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'Kinesis'
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  HtbNotifyKinesisStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub ${AwsLogicalAccountNameLowerCase}-update-events-htb-${Environment}
      RetentionPeriodHours: 24
      ShardCount: 1
      StreamEncryption:
        EncryptionType: KMS
        KeyId: 'alias/aws/kinesis'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'Kinesis'
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  HtbCdrEventsKinesisStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub ${AwsLogicalAccountNameLowerCase}-cdr-events-htb-${Environment}
      RetentionPeriodHours: 24
      ShardCount: 1
      StreamEncryption:
        EncryptionType: KMS
        KeyId: 'alias/aws/kinesis'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'Kinesis'
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  HtbChargepointEventsKinesisStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub ${AwsLogicalAccountNameLowerCase}-chargepoint-events-htb-${Environment}
      RetentionPeriodHours: 24
      ShardCount: 1
      StreamEncryption:
        EncryptionType: KMS
        KeyId: 'alias/aws/kinesis'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'Kinesis'
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  ChargepointEventsKinesisStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub ${AwsLogicalAccountNameLowerCase}-chargepoint-events-${Environment}
      RetentionPeriodHours: 24
      ShardCount: !Ref ChargepointEventsKinesisShardCount
      StreamEncryption:
        EncryptionType: KMS
        KeyId: 'alias/aws/kinesis'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'Kinesis'
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  DCSCDREventsLambdaKinesis:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-CDR-Events-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: dcsCdrEvents/index.handler
      Timeout: 300
      ReservedConcurrentExecutions: 1
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/dcsCdrEvents/dist
      Environment:
        Variables:
          API_GATEWAY_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayKey}:SecretString:secret}}'
          API_GATEWAY_STREAM_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayStreamKey}:SecretString:secret}}'
          PRIVATE_GATEWAY_SERVER_HTTP: !Ref PrivateGatewayServerHttp
          APOLLO_INTERNAL_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApolloInternalSecret}:SecretString:secret}}'
          NODE_ENV: !Ref NodeEnv
          REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME: !Ref RegisteredChargeProcessingLambdaName
          CREDIT_CDR_FEATURE_FLAG: !Ref CreditCDRFeatureFlag
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  DCSCDREventsLambdaKinesisEvent:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt DcsCdrKinesisStream.Arn
      FunctionName: !GetAtt DCSCDREventsLambdaKinesis.Arn
      StartingPosition: LATEST
      BatchSize: 1
      Enabled: True

  HTBNotifyLambdaKinesis:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Notify-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 120
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/htbNotifyEvents
      Environment:
        Variables:
          ELASTICACHE_HOST: !GetAtt EventsElastiCache.Outputs.ElastiCacheEndpoint
          ELASTICACHE_PORT: !GetAtt EventsElastiCache.Outputs.ElastiCachePort
          INTERNAL_BPPAY_SERVER_HTTP: !Ref InternalBppayServerHttp
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  HTBNotifyLambdaKinesisEvent:
    Type: AWS::Lambda::EventSourceMapping
    Properties:
      EventSourceArn: !GetAtt HtbNotifyKinesisStream.Arn
      FunctionName: !GetAtt HTBNotifyLambdaKinesis.Arn
      StartingPosition: LATEST
      BatchSize: 10
      Enabled: True

  HTBTokenElastiCache:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        Environment: !Ref Environment
        Service: !Ref Service
        ApiVersion: !Ref ApiVersion
        StackName: !Ref StackName
        AwsLogicalAccountNameLowerCase: !Ref AwsLogicalAccountNameLowerCase
        RedisNodeType: !Ref RedisNodeType
        RedisCacheSubnetGroup: !Ref RedisCacheSubnetGroup
        RedisGroupDescription: 'Htb Token Cache'
        RedisGroupID: htb-token
        ElastiCacheSecurityGroups: !Ref ElastiCacheSecurityGroups
        CurrentDate: !Ref CurrentDate
        ServiceVersion: !Ref ServiceVersion
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/elasticache/elasticache.yaml

  HTBTokenRefreshLambdaEvents:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Token-Refresh-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 512
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 300
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/htbTokenRefresh
      Events:
        ScheduleEvent:
          Type: Schedule
          Properties:
            Description: 'Lambda Trigger'
            Enabled: True
            Name: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Token-Refresh-Trigger-${ApiVersion}-${Environment}
            Schedule: 'rate(2 minutes)'
      Environment:
        Variables:
          TOKEN_ELASTICACHE_HOST: !GetAtt HTBTokenElastiCache.Outputs.ElastiCacheEndpoint
          ELASTICACHE_PORT: !GetAtt HTBTokenElastiCache.Outputs.ElastiCachePort
          HTB_SERVICES_USERNAME_GUEST: !Ref HtbServicesUsernameGuest
          HTB_SERVICES_PASSWORD_GUEST: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordGuest}:SecretString:secret}}'
          HTB_SERVICES_IDENTIFIER_GUEST: !Ref HtbServicesIdentifierGuest
          HTB_SERVICES_IDENTIFIER_NL: !Ref HtbServicesIdentifierNL
          HTB_SERVICES_IDENTIFIER_DE: !Ref HtbServicesIdentifierDE
          HTB_SERVICES_IDENTIFIER_ES: !Ref HtbServicesIdentifierES
          HTB_SERVICES_BEARER: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesBearerToken}:SecretString:secret}}'
          HTB_SERVICES_URL: !Ref HtbServicesUrl
          HTB_SERVICES_USERNAME_DE: !Ref HtbServicesUsernameDE
          HTB_SERVICES_PASSWORD_DE: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordDE}:SecretString:secret}}'
          HTB_SERVICES_USERNAME_NL: !Ref HtbServicesUsernameNL
          HTB_SERVICES_PASSWORD_NL: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordNL}:SecretString:secret}}'
          HTB_SERVICES_USERNAME_ES: !Ref HtbServicesUsernameES
          HTB_SERVICES_PASSWORD_ES: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordES}:SecretString:secret}}'
          HTB_SERVICES_USERNAME_UK: !Ref HtbServicesUsernameUK
          HTB_SERVICES_PASSWORD_UK: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${HtbServicesPasswordUK}:SecretString:secret}}'
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  DCSTokenRefreshLambdaEvents:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-Token-Refresh-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 128
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 300
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/dcsTokenRefresh
      Environment:
        Variables:
          TOKEN_ELASTICACHE_HOST: !GetAtt HTBTokenElastiCache.Outputs.ElastiCacheEndpoint
          ELASTICACHE_PORT: !GetAtt HTBTokenElastiCache.Outputs.ElastiCachePort
          DCS_TOKEN_CLIENT_ID: !Ref DcsTokenClientId
          DCS_TOKEN_CLIENT_SECRET: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${DcsTokenClientSecret}:SecretString:secret}}'
          DCS_TOKEN_RESOURCE: !Ref DcsTokenResource
          DCS_TOKEN_URL: !Ref DcsTokenUrl
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  DCSBulkExportCdrLambdaEvents:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 256
      PackageType: Zip
      Runtime: nodejs20.x
      Handler: src/index.handler
      Timeout: 900
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/dcsBulkExportCdr/dist
      Events:
        ScheduleEvent:
          Type: Schedule
          Properties:
            Description: 'Lambda Trigger'
            Enabled: True
            Name: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-Trigger-${ApiVersion}-${Environment}
            Schedule: 'cron(0 * * * ? *)'
      Environment:
        Variables:
          DRY_RUN: false
          START_PROCESSING_DATE: 2025-06-17
          DCS_ACCOUNT_URL: !Ref DcsAccountUrl
          DCS_SAS_TOKEN: !Ref DcsSasToken
          DCS_DIRECTORY_NAME: !Ref DcsDirectoryName
          DCS_SHARE_NAME: !Ref DcsShareName
          DCS_BUCKET_NAME: !Ref DcsOutboundRoamingBucketName
          S3_REGION: !Ref S3Region
          DCS_CDR_EVENTS_GATEWAY_STREAM_URL: !Ref DcsCdrEventsGatewayStreamUrl
          API_GATEWAY_STREAM_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${ApiVersion}-${Stack}-${ApiGatewayStreamKey}:SecretString:secret}}'
          NODE_ENV: !Ref NodeEnv

      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  DCSOutboundRoamingS3Bucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    Properties:
      AccessControl: Private
      BucketName: !Sub ${AwsLogicalAccountNameLowerCase}-dcs-outbound-roaming-${ApiVersion}-${Environment}
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  DcsCdrS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      AccessControl: 'Private'
      BucketName: !Sub ${AwsLogicalAccountNameLowerCase}-dcs-cdr-export-${Environment}
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
    DeletionPolicy: Retain

  DcsCdrBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DeletionPolicy: Retain
    Properties:
      Bucket: !Ref 'DcsCdrS3Bucket'
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Sid: AllowSSLRequestsOnly
            Action:
              - 's3:*'
            Effect: Deny
            Resource:
              - !Join
                - ''
                - - 'arn:aws:s3:::'
                  - !Ref 'DcsCdrS3Bucket'
                  - '/*'
              - !Join
                - ''
                - - 'arn:aws:s3:::'
                  - !Ref 'DcsCdrS3Bucket'
            Principal: '*'
            Condition:
              Bool:
                aws:SecureTransport: false

  ChargePointAvailabilityS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      AccessControl: 'Private'
      BucketName: !Sub ${AwsLogicalAccountNameLowerCase}-chargepoint-availability-s3-bucket-${Environment}
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
    DeletionPolicy: Retain

  ChargePointAvailabilityLambda:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
      CodeUri: lambdas/chargePointAvailabilityCheck/dist
      Handler: chargePointAvailabilityCheck/index.handler
      Runtime: nodejs18.x
      MemorySize: 512
      Timeout: 900
      Role: !Ref LambdaRoleArn
      Tracing: Active
      PackageType: Zip
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      Events:
        DailySchedule:
          Type: Schedule
          Properties:
            Description: 'Daily run to export charge point availability to S3'
            Enabled: true
            Name: !Sub ${AwsLogicalAccountNameUpperCase}-ChargePointAvailability-Schedule-${ApiVersion}-${Environment}
            Schedule: cron(0 2 * * ? *) # Daily at 2AM UTC
      Environment:
        Variables:
          CHARGEPOINT_AVAILABILITY_BUCKET: !Ref ChargePointAvailabilityS3Bucket
          OTG_URL_V7: !Ref ApolloClientUrl
          OTG_API_KEY_V7: !Ref ApolloClientKey
          CHARGEVISION_TOKEN_ENDPOINT: !Ref ChargevisionTokenEndpoint
          CHARGEVISION_URL: !Ref ChargevisionUrl
          CHARGEVISION_AUTH_KEY: !Ref ChargevisionAuthKey
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        apiVersion: !Ref ApiVersion
        stack: !Ref StackName
        serviceVersion: !Ref ServiceVersion

  ChargePointAvailabilityS3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    DeletionPolicy: Retain
    Properties:
      Bucket: !Ref ChargePointAvailabilityS3Bucket
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Sid: AllowLambdaPutGet
            Effect: Allow
            Principal:
              AWS: !Ref LambdaRoleArn
            Action:
              - s3:PutObject
              - s3:GetObject
            Resource: !Sub arn:aws:s3:::${AwsLogicalAccountNameLowerCase}-chargepoint-availability-s3-bucket-${Environment}/*
          - Sid: AllowSSLRequestsOnly
            Effect: Deny
            Principal: '*'
            Action: 's3:*'
            Resource:
              - !Join [
                  '',
                  ['arn:aws:s3:::', !Ref ChargePointAvailabilityS3Bucket, '/*'],
                ]
              - !Join [
                  '',
                  ['arn:aws:s3:::', !Ref ChargePointAvailabilityS3Bucket],
                ]
            Condition:
              Bool:
                aws:SecureTransport: false

Outputs:
  ChargepointEventsRedisClusterEndpointAddress:
    Description: 'The address of the Chargepoints events Redis cluster endpoint'
    Value: !GetAtt EventsElastiCache.Outputs.ElastiCacheEndpoint
    Export:
      Name: !Sub ChargepointEventsRedisClusterEndpointAddress-${Environment}

  ChargepointEventsRedisClusterEndpointPort:
    Description: 'The port of the Chargepointsevents Redis cluster endpoint'
    Value: !GetAtt EventsElastiCache.Outputs.ElastiCachePort
    Export:
      Name: !Sub ChargepointEventsRedisClusterEndpointPort-${Environment}

  DcsCdrS3Name:
    Value: !Ref 'DcsCdrS3Bucket'
