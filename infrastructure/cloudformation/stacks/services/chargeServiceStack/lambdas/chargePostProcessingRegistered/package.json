{"name": "charge-post-processing-registered", "private": true, "main": "src/index.js", "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "test": "jest", "test:dev": "jest --watch", "types:check": "tsc --noEmit"}, "dependencies": {"aws-sdk": "^2.1425.0", "axios": "1.10.0", "dotenv": "^16.4.5", "graphql": "^15.5.3", "graphql-request": "^3.5.0", "uuid": "^8.3.2", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/jest": "^27.4.1", "@types/node": "^18.11.2", "@types/uuid": "^9.0.8", "babel-jest": "^28.0.2", "jest": "^27.5.1", "typescript": "^4.6.3"}}