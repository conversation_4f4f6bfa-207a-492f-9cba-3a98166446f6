import Utils from '../../shared/utils';
import creditCards from '../../test-data/wallet/creditCards.json';
import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as userHelpers from '../../shared/userHelpers';
import * as common from '../../shared/common';
import * as paymentHelper from '../../shared/paymentHelper';
import * as historyHelper from '../../shared/historyHelper';
import * as Const from '../../shared/constants';
import chargepoints from '../../test-data/charge/chargePoints.json';
import searchConnectorByIdSchema from '../../test-data/schema/searchConnector.json';
import getChargepointByConnectorIdSchema from '../../test-data/schema/getChargepointByConnectorId.json';
import getUserChargeEventSchema from '../../test-data/schema/getUserChargeEvent.json';
import getUserChargeEventSchemaUK from '../../test-data/schema/getUserChargeEventUK.json';
import getLatestHistoryRecordSchema from '../../test-data/schema/getLatestHistoryRecord.json';

const testUsers = [
  users.find((user) => user.testScope === 'charge_UK'),
  users.find((user) => user.testScope === 'charge_DE'),
  users.find((user) => user.testScope === 'charge_ES'),
];

testUsers.forEach((user) => {
  describe(user.countryCode + ' user is  able to charge', () => {
    jest.setTimeout(200_000);
    const chargePointId = user.chargepoints[0];
    const chargePoint = chargepoints.find(
      (chargepoints) => chargepoints.providerExternalId === chargePointId,
    );
    const chargeToPercent = 25;

    let testContext = {
      token: '',
      codeChallenge: '',
      availableConnector: '',
      apolloInternalId: '',
      cardToAdd: '',
      cpAddress: null,
      startCharge: '',
    };

    let paymentMethods;

    const chargeSessionFile =
      Const.TEMP_FOLDER + user.testScope + '_chargeSession.json';

    beforeAll(async () => {
      testContext.token = await common.loginUser(user);
      user.externalId = Utils.getJwtPayload(testContext.token).external_id;
      Utils.deleteFileIfExists(chargeSessionFile); // charge session from previous test

      let responseChargeEvent = await userHelpers.getUserChargeEvent(
        testContext.token,
        user.externalId,
      );
      expect(responseChargeEvent.status).toBe(200);

      if (
        responseChargeEvent.body.data.getUserChargeEvent.eventDetails !== null
      ) {
        const connectorInternalId =
          responseChargeEvent.body.data.getUserChargeEvent.chargePayload
            .connectorInternalId;
        const responseConnectors = await common.getChargepointByConnectorId(
          testContext.token,
          [connectorInternalId],
        );
        expect(responseConnectors.status).toBe(200);
        expect(responseConnectors.body.data.connectors.length).toBeGreaterThan(
          0,
        );
        const apolloInternalId =
          responseConnectors.body.data.connectors[0].chargepoint
            .apolloInternalId;
        expect(apolloInternalId.length).toBeGreaterThan(0);

        const responseStopCharge = await common.stopCharge(
          user.externalId,
          testContext.token,
          apolloInternalId,
          connectorInternalId,
        );

        expect(responseStopCharge.status).toBe(200);

        const expectedMessages = [
          'stop charge',
          'queueStopCharge successfully called for DCS',
        ];

        const hasExpectedMessages = expectedMessages.some((el) =>
          responseStopCharge.body.data.stopCharge.message.includes(el),
        );

        expect(hasExpectedMessages).toBe(true);

        const startTime = Date.now();
        const timeoutMs = 60_000;
        while (Date.now() - startTime <= timeoutMs) {
          responseChargeEvent = await userHelpers.getUserChargeEvent(
            testContext.token,
            user.externalId,
          );
          expect(responseChargeEvent.status).toBe(200);
          let eventDetail =
            responseChargeEvent.body.data.getUserChargeEvent.eventDetails;
          if (eventDetail === 'STOP_CHARGE_RESPONSE') {
            break;
          }
          await Utils.delay(2_000);
        }

        const responseClearChargeSession = await common.clearChargeSession(
          user.externalId,
          testContext.token,
        );
        expect(responseClearChargeSession.status).toBe(200);
        expect(
          responseClearChargeSession.body.data.clearChargeSession.message,
        ).toContain('delete');
        responseChargeEvent = await userHelpers.getUserChargeEvent(
          testContext.token,
          user.externalId,
        );
        const eventDetail =
          responseChargeEvent.body.data.getUserChargeEvent.eventDetails;
        expect(eventDetail).toBe(null);
      }

      //add payment method if it doesn't exists or is in 'pending deletion state' to be able to charge
      let paymentMethodsResponse = await paymentHelper.getPaymentMethodsWallet(
        user.externalId,
        testContext.token,
      );
      expect(paymentMethodsResponse.status).toBe(200);
      paymentMethods = paymentMethodsResponse.body.data.getPaymentMethodsWallet;

      if (
        !paymentMethods ||
        (paymentMethods.length <= 1 && paymentMethods[0]?.deleteDate != null)
      ) {
        testContext.cardToAdd = Utils.getRandomObject(creditCards);
        let spreadlyResponse = await paymentHelper.getSpreadlyToken(
          testContext.cardToAdd,
        );
        expect(spreadlyResponse.status).toBe(201);
        const tokenSpreadly =
          spreadlyResponse.body.transaction.payment_method.token;
        expect(tokenSpreadly.length).toBeGreaterThan(0);

        let response = await paymentHelper.addPaymentMethods(
          user,
          testContext.token,
          tokenSpreadly,
        );
        expect(response.status).toBe(200);
        expect(
          response.body.data.addPaymentMethodWallet.length,
        ).toBeGreaterThan(0);

        paymentMethodsResponse = await paymentHelper.getPaymentMethodsWallet(
          user.externalId,
          testContext.token,
        );
        expect(paymentMethodsResponse.status).toBe(200);
        paymentMethods =
          paymentMethodsResponse.body.data.getPaymentMethodsWallet;
        expect(paymentMethods.length).toBeGreaterThan(0);
        expect(paymentMethods[0].deleteDate).toBe(null);
      }
    });

    it('search charge point ' + chargePoint.providerExternalId, async () => {
      let response = await common.searchConnectorById(
        chargePoint.providerExternalId,
        testContext.token,
      );
      expect(response.status).toBe(200);
      expect(
        Utils.validateSchema(searchConnectorByIdSchema, response.body),
      ).toBe(true);
      expect(response.body.data.results.length).toBeGreaterThan(0);
      expect(response.body.data.results.length).toBeGreaterThan(0);
      testContext.availableConnector = Utils.findAvailableConnector(
        response.body,
      );
      expect(testContext.availableConnector).not.toBeNull();
    });

    it('clear charge session', async () => {
      let response = await common.clearChargeSession(
        user.externalId,
        testContext.token,
      );
      expect(response.status).toBe(200);
      const message = response.body.data.clearChargeSession.message;
      expect(
        message === 'cache item delete' ||
          message === 'key not found to delete cache item',
      ).toBe(true);
    });

    it(
      'get chargepoint by connector id' + testContext.availableConnector,
      async () => {
        let response = await common.getChargepointByConnectorId(
          testContext.token,
          testContext.availableConnector,
        );
        expect(response.status).toBe(200);
        expect(
          Utils.validateSchema(
            getChargepointByConnectorIdSchema,
            response.body,
          ),
        ).toBe(true);
        const data = response.body.data.connectors;
        const actualConnector = data.find(
          (item) => item.connectorInternalId === testContext.availableConnector,
        );
        expect(actualConnector.chargepoint.provider).toBe(chargePoint.provider);
        expect(actualConnector.chargepoint.site.siteDetails.address).toBe(
          chargePoint.site.siteDetails.address,
        );
        expect(actualConnector.chargepoint.site.siteDetails.country).toBe(
          chargePoint.site.siteDetails.country,
        );

        testContext.cpAddress =
          response.body.data.connectors[0].chargepoint.site.siteDetails;
        expect(response.body.data.connectors.length).toBeGreaterThan(0);
        expect(response.body.data.connectors[0].chargepoint.apolloInternalId)
          .not.toBeNull;

        testContext.apolloInternalId =
          response.body.data.connectors[0].chargepoint.apolloInternalId;
        expect(testContext.apolloInternalId).not.toBeNull;
        expect(
          response.body.data.connectors[0].chargepoint.providerExternalId,
        ).toBe(chargePoint.providerExternalId);
      },
    );

    it('start charge', async () => {
      const appCountry = user.countryCode;
      const paymentMethodId = paymentMethods[0].paymentMethodId;
      const threeDS = {};
      const paymentId = Utils.generateUUID();

      if (user.paymentAuthType === 'PREAUTH') {
        await common.registeredPreAuth({
          userId: user.externalId,
          appCountry,
          paymentId,
          paymentMethodId,
          threeDS,
          token: testContext.token,
        });
      }

      let response = await common.startCharge(
        user.externalId,
        testContext.token,
        testContext.apolloInternalId,
        testContext.availableConnector,
      );
      expect(response.status).toBe(200);
      expect(response.body.data.startCharge.message).toBe(
        'charge session in progress',
      );
    });

    it('get user charge event', async () => {
      let response = await userHelpers.getUserChargeEvent(
        testContext.token,
        user.externalId,
      );
      expect(response.status).toBe(200);

      let message = response.body.data.getUserChargeEvent.eventDetails;
      /*
      message for getUserChargeEvent() is START_CHARGE_ACCEPTED for the first call which sometimes cannot be caught
      then the next messages are START_CHARGE_RESPONSE
      */
      expect(
        message === 'START_CHARGE_ACCEPTED' ||
          message === 'START_CHARGE_RESPONSE',
      ).toBe(true);
      const payload = response.body.data.getUserChargeEvent.chargePayload;
      expect(payload.connectorInternalId).toBe(testContext.availableConnector);
      expect(payload.status === 'Success' || payload.status === 'ok').toBe(
        true,
      );
      testContext.startCharge = response.body.data.getUserChargeEvent.eventTime;
    });

    it(
      'wait until the stateOfCharge is greater then ' + chargeToPercent + '%',
      async () => {
        let responseChargeEvent;
        let stateOfCharge = 0;
        let currentPrice = 0;

        const startTime = Date.now();
        const timeoutMs = 60_000;
        while (Date.now() - startTime <= timeoutMs) {
          // waiting until the charging progress reaches 'chargeToPercent' value
          responseChargeEvent = await userHelpers.getUserChargeEvent(
            testContext.token,
            user.externalId,
          );
          expect(responseChargeEvent.status).toBe(200);

          let stateOfChargeObj =
            responseChargeEvent.body.data.getUserChargeEvent
              .chargeMonitoringPayload.stateOfCharge;
          if (stateOfChargeObj != null && stateOfChargeObj.value != null) {
            stateOfCharge = parseInt(stateOfChargeObj.value);
          }

          let currentPriceObj =
            responseChargeEvent.body.data.getUserChargeEvent
              .chargeMonitoringPayload.currentPrice;
          if (currentPriceObj != null && currentPriceObj.value != null) {
            currentPrice = parseInt(currentPriceObj.value);
          }

          if (stateOfCharge > chargeToPercent && currentPrice > 0) {
            break;
          }
          await Utils.delay(2_000);
        }

        if (user.countryCode === 'UK') {
          expect(
            Utils.validateSchema(
              getUserChargeEventSchemaUK,
              responseChargeEvent.body,
            ),
          ).toBe(true);
        } else {
          expect(
            Utils.validateSchema(
              getUserChargeEventSchema,
              responseChargeEvent.body,
            ),
          ).toBe(true);
        }
      },
    );

    it('stop the charging', async () => {
      const responseStopCharge = await common.stopCharge(
        user.externalId,
        testContext.token,
        testContext.apolloInternalId,
        testContext.availableConnector,
      );
      expect(responseStopCharge.status === 200).toBeTruthyWithMessage(
        'Unexpected response \n' + JSON.stringify(responseStopCharge, null, 2),
      );

      const expectedMessages = [
        'stop charge',
        'queueStopCharge successfully called for DCS',
      ];

      const hasExpectedMessages = expectedMessages.some((el) =>
        responseStopCharge.body.data.stopCharge.message.includes(el),
      );

      expect(hasExpectedMessages).toBe(true);

      let responseChargeEvent;
      const startTime = Date.now();
      const timeoutMs = 60_000;
      while (Date.now() - startTime <= timeoutMs) {
        // waiting until the eventDetail is 'STOP_CHARGE_RESPONSE'
        responseChargeEvent = await userHelpers.getUserChargeEvent(
          testContext.token,
          user.externalId,
        );
        expect(responseChargeEvent.status).toBe(200);
        let eventDetail =
          responseChargeEvent.body.data.getUserChargeEvent.eventDetails;
        let status =
          responseChargeEvent.body.data.getUserChargeEvent.chargePayload.status;
        if (eventDetail === 'STOP_CHARGE_RESPONSE' && status === 'Success') {
          break;
        }
        await Utils.delay(2_000);
      }

      const responseClearChargeSession = await common.clearChargeSession(
        user.externalId,
        testContext.token,
      );
      expect(responseClearChargeSession.status).toBe(200);
      expect(
        responseClearChargeSession.body.data.clearChargeSession.message,
      ).toContain('delete');
    });

    it('save the charge session details for the transactionHistory and the invoice tests', async () => {
      let responseLatestHistoryRecord;
      const startTime = Date.now();
      const timeoutMs = 10_000;
      while (Date.now() - startTime <= timeoutMs) {
        // waiting for the charging record to be created
        responseLatestHistoryRecord =
          await historyHelper.getLatestHistoryRecord(testContext.token, user);
        expect(responseLatestHistoryRecord.status).toBe(200);

        const energyConsumed =
          responseLatestHistoryRecord.body.data.getLatestHistoryRecord
            .chargeDetails.energyConsumed;
        if (energyConsumed.value && energyConsumed.value > 0) {
          break;
        }
        await Utils.delay(2_000);
      }

      const stateOfCharge =
        responseLatestHistoryRecord.body.data.getLatestHistoryRecord
          .chargeDetails.stateOfCharge;
      if (!stateOfCharge.value) {
        // monitoring the stateOfCharge value
        console.warn('stateOfCharge.value is null');
      }

      expect(
        Utils.validateSchema(
          getLatestHistoryRecordSchema,
          responseLatestHistoryRecord.body,
        ),
      ).toBeTruthyWithMessage(
        'Schema validation failed for the response: \n' +
          JSON.stringify(responseLatestHistoryRecord.body, null, 2),
      );

      const latestHistoryRecord =
        responseLatestHistoryRecord.body.data.getLatestHistoryRecord;
      expect(latestHistoryRecord).not.toBeNull();
      expect(latestHistoryRecord.chargeSessionId).not.toBeNull();
      expect(latestHistoryRecord.chargepoint.address).toBe(
        testContext.cpAddress.address,
      );
      expect(latestHistoryRecord.chargepoint.postcode).toBe(
        testContext.cpAddress.postcode,
      );
      expect(latestHistoryRecord.chargepoint.country).toBe(
        testContext.cpAddress.country,
      );
      expect(latestHistoryRecord.chargepoint.city).toBe(
        testContext.cpAddress.city,
      );
      expect(
        Utils.parseNumber(latestHistoryRecord.chargeDetails.totalGross),
      ).toBeGreaterThan(0);
      expect(
        latestHistoryRecord.chargeDetails.energyConsumed.value,
      ).toBeGreaterThan(0);
      Utils.saveStringToFile(
        chargeSessionFile,
        JSON.stringify(latestHistoryRecord, null, 2),
      );
    });
  });
});
