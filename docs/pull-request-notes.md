# Guidelines for Pull Requests

## Contents

- [Introduction](#introduction)
- [Testing](#testing)
  - [Testing API calls](#testing-api-calls)
  - [Testing user-server changes](#testing-user-server-changes)
- [Environment variables](#environment-variables)
- [Aurora DB migrations](#aurora-db-migrations)
- [Additional documentation](#additional-documentation)

## Introduction

- It is likely that multiple people across teams are working on the same code at the same time, making it hard to keep track of code changes and increasing the likelihood of breaking changes.
- When submitting a PR to the backend, it's therefore good to be mindful of how these changes might affect other teams and try and mitigate any issues in the PR environment before they are merged to dev.
- These guidelines are hopefully a good starting point for those who are newer to the backend or haven't worked on a particular area before, and include links to useful documentation.

  ***

## Testing

- Changes should be covered by unit/contract tests and functionally tested locally or within the PR env before being merged into dev, unless there is a specific reason why this is not possible.
- It is important that changes to existing services/stacks are backwards compatible, so extra care should be taken in these cases.
- User server changes are especially prone to breaking changes, so please follow the specific guidelines

#### Testing API calls

- API calls can be tested using API testing software (NOT POSTMAN, Insomnia is one currently approved)
- Calls and environment config (with git secrets) can be found in the postman folder of the repo - new ones should also be added here
- Most requests can be tested by obtaining a QA Token for a user and then making the request using that token in the Authorization field - see [this video](https://bp365-my.sharepoint.com/:v:/r/personal/millisande_bath_bp_com1/Documents/Postman%20variables%20brownbag.mp4?csf=1&web=1&e=oEH6B6&nav=eyJwbGF5YmFja09wdGlvbnMiOnt9LCJyZWZlcnJhbEluZm8iOnsicmVmZXJyYWxBcHAiOiJTdHJlYW1QbGF5bGlzdCIsInJlZmVycmFsTW9kZSI6Im1pcyIsInJlZmVycmFsVmlldyI6InZpZGVvYWN0aW9ucy1zaGFyZSIsInJlZmVycmFsUGxheWJhY2tTZXNzaW9uSWQiOiI1NDIyYjAwMy1hZTFhLTQ0MzctYTZjOC0yYmJlZWYyM2FmN2QifX0%3D) for a walkthrough guide
- Some requests are internal only - these can be tested in the jumpbox using CURL requests, see wiki [here](https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/105826/Using-the-Bastion-Jumpbox-When-and-How-)

#### Testing user server changes

- The user server is especially prone to breaking changes and so any changes need to be tested against all user types.
- Please test against the following QA users:
  - NL, DE or ES PAYG-Wallet user
  - UK SUBS and PAYG user
  - UK Subs-Wallet and PAYG-WALLET user
- QA users can be found in [this wiki](https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/167052/Test-accounts)
- Be especially wary with database changes to existing tables, see [the migrations section](#aurora-db-migrations) for more info

---

## Environment variables

- New env vars need to be added in multiple places in the code:
  1. The env.ts file
  2. The variables.json file
  3. The deployment manifest
  4. The local .env (hidden with `git secret hide`)
- The deployment pipelines check for the presence of env vars, so they also need to be added to either the library group/secrets manager across all envs:

  - Lib group: [Pulse-Apps Microservices-env](https://dev.azure.com/bp-digital/bp_pulse/_library?itemType=VariableGroups)
  - [Pipeline to add secrets to secrets manager](https://dev.azure.com/bp-digital/bp_pulse/_build?definitionId=37116)

  ***

## Aurora DB Migrations

- If a migration has been added, it needs to be deployed to Aurora manually using the [Pulse-Apps-Aurora-DB-Sync](https://dev.azure.com/bp-digital/bp_pulse/_build?definitionId=30750) pipeline.
- Migrations may cause breaking changes, especially to the user-server, so please refer to the [Breaking Changes wiki](https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/164613/Managing-breaking-database-updates) for more information on how to manage these.

---

## Debugging PR pipeline issues

#### Sonarqube issues

- You will need ZScaler set up for Sonarqube access - how to access [here](https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/105784/5-ZScaler-Set-Up)

- If the Sonarqube scan fails, view the 'Execute Sonar Analysis' step in the pipeline and scroll to the bottom to view the link to the report.
- Log into Sonarqube with SAML and press Advanced -> Proceed anyway if the browser warns about connection privacy

#### Failing contract tests

- If contract tests are failing for your server PR and there is no known wider issue, it can be useful to look into the pod logs for the server deployment
  1. Follow the steps to get Kubernetes pod logs locally [here](https://dev.azure.com/bp-digital/DST-Digital_AMU/_wiki/wikis/DST-Digital_AMU.wiki/64274/Accessing-Kubernetes-Logs)
  2. Once logged in to the AWS CLI, run the command
     `aws eks update-kubeconfig --name WS-00F0-Pulse-Apps-Services-vx-pr --profile WS-00F0-role_ENGINEERING --region eu-west-2` to access the PR env
  3. Run the 'Deploy' step of the pipeline, once the gateway server has restarted check the gateway pod logs first using the above guide. The gateway will often point towards the issue, and you can check the relevant server pod logs too.
  4. You can only check these logs between the two gateway server restart steps, so be quick!

---

## Additional Documentation

- [Overview of environments and deployments](https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/181921/PR-environment-and-how-the-new-environment-structure-works)
- [Accessing Cloudwatch Logs for AWS](https://dev.azure.com/bp-digital/DST-Digital_AMU/_wiki/wikis/DST-Digital_AMU.wiki/50824/Accessing-Logs)
- [Brown bags](https://bp365.sharepoint.com/:l:/s/Apollo11/FD1KOCJLMSpPlP0LD0aXozIB_GfxfrKdoX6VPLLE-chJvw?email=hannah.austin%40bp.com&e=tE550z)
