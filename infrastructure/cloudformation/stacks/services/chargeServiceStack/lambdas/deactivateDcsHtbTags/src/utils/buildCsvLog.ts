export function buildCsvLog(
    rows: Array<{
        userId: string;
        tag: any;
        country: string;
        status: string;
        error?: string;
        csvLogId: string;
    }>,
    csvLogId: string,
): string {
    let csv =
        'uniqueidentifier,UserId,tagNotes,tagId,country,deactivate status,tagCardNumber,tagStatus,error';
    rows.forEach(({ userId, tag, country, status, error, csvLogId }) => {
        csv += `\n${csvLogId},${userId || ''},${tag.tagNotes || ''},${
            tag.tagId || ''
        },${country || ''},${status},${tag.tagCardNumber || ''},${
            tag.tagStatus || ''
        },${error || ''}`;
    });
    return csv;
}

function getTagRows({
    userId,
    tags,
    country,
    status,
    error,
    csvLogId,
}: {
    userId: string;
    tags: any[];
    country: string;
    status: string;
    error?: string;
    csvLogId: string;
}) {
    if (!tags || tags.length === 0) {
        return [{ userId, tag: {}, country, status, error, csvLogId }];
    }
    return tags.map((tag: any) => ({
        userId,
        tag,
        country,
        status,
        error,
        csvLogId,
    }));
}

export function buildCsvRowsFromResults(
    results: any[],
    validatedEvents: any,
    csvLogId: string,
): Array<{
    userId: string;
    tag: any;
    country: string;
    status: string;
    error?: string;
    csvLogId: string;
}> {
    const csvRows: Array<{
        userId: string;
        tag: any;
        country: string;
        status: string;
        error?: string;
        csvLogId: string;
    }> = [];
    results.forEach((r) => {
        if (r.result) {
            const { userId, result } = r;
            const status = result.success ? 'success' : 'failed';
            let country = '';
            if (
                Array.isArray(validatedEvents) &&
                validatedEvents[r.eventIndex]
            ) {
                country = validatedEvents[r.eventIndex].country || '';
            }
            const allTags = [
                ...(result.successfullyTerminated || []),
                ...(result.failedToTerminate || []),
            ].map((entry) => ({ ...entry.tag, error: entry.error }));
            csvRows.push(
                ...getTagRows({
                    userId,
                    tags: allTags,
                    country,
                    status,
                    csvLogId,
                }),
            );
        } else {
            const { userId, tags = [], country, error } = r;
            csvRows.push(
                ...getTagRows({
                    userId,
                    tags,
                    country,
                    status: 'failed',
                    error,
                    csvLogId,
                }),
            );
        }
    });
    return csvRows;
}

export function buildCsvRowsFromEventError(
    event: any,
    error: any,
    csvLogId: string,
): Array<{
    userId: string;
    tag: any;
    country: string;
    status: string;
    error?: string;
    csvLogId: string;
}> {
    const csvRows: Array<{
        userId: string;
        tag: any;
        country: string;
        status: string;
        error?: string;
        csvLogId: string;
    }> = [];
    const getErrorMsg = (err: any) =>
        err instanceof Error ? err.message : String(err);
    if (Array.isArray(event)) {
        (event || []).forEach((singleEvent: any) => {
            const country = singleEvent.country || '';
            const userId = singleEvent.userId || '';
            const tagIds = Array.isArray(singleEvent.tagIds)
                ? singleEvent.tagIds
                : [];
            csvRows.push(
                ...getTagRows({
                    userId,
                    tags: tagIds,
                    country,
                    status: 'failed',
                    error: getErrorMsg(error),
                    csvLogId,
                }),
            );
        });
    } else {
        const singleEvent: any = event || {};
        const country = singleEvent.country || '';
        const userId = singleEvent.userId || '';
        const tagIds = Array.isArray(singleEvent.tagIds)
            ? singleEvent.tagIds
            : [];
        csvRows.push(
            ...getTagRows({
                userId,
                tags: tagIds,
                country,
                status: 'failed',
                error: getErrorMsg(error),
                csvLogId,
            }),
        );
    }
    return csvRows;
}
