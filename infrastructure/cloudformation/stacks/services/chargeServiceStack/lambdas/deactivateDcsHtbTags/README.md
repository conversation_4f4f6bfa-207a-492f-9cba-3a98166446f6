# Deactivate DCS/HTB Tags Lambda

## Overview

This AWS Lambda function processes deactivation requests for DCS, HTB, and
physical RFID tags.

## Supported Tag Types

-   **DCS Tags**: `virtual-DCS` - Virtual tags for DCS provider
-   **HTB Tags**: `virtual-HTB` - Virtual tags for HTB/HASTOBE provider
-   **Physical RFID Tags**: `physical-RFID` - Physical RFID cards

## Supported Countries

-   **NL** (Netherlands)
-   **DE** (Germany)
-   **ES** (Spain)

## Environment Variables

The Lambda requires several environment variables for DCS/HTB API credentials
and endpoints. These are configured in the CloudFormation template.

## Behavior by Tag Type

### Virtual DCS Tags (`virtual-DCS`)

-   Terminates DCS contracts via DCS API
-   Updates tag status to TERMINATED in internal systems

### Virtual HTB Tags (`virtual-HTB`)

-   Deactivates HTB cards via HTB/HASTOBE API
-   Updates tag status to TERMINATED in internal systems

### Physical RFID Tags (`physical-RFID`)

-   Requires a valid `userId` parameter
-   Blocks RFID card via internal blocking service
-   Only processes ACTIVE physical RFID tags
-   Uses "Request to Can<PERSON>" as the blocking reason

## Tag Selection Logic

When both `userId` and `tagIds` are provided:

-   The function prioritizes `userId` and deactivates **all eligible user tags**
-   The `tagIds` parameter is ignored (with a warning logged)
-   Only processes tags with recognized tag types (`virtual-DCS`, `virtual-HTB`,
    `physical-RFID`)
-   Physical RFID tags must have ACTIVE status to be processed

## Input Requirements

-   **country**: Required field - must be one of: `NL`, `DE`, `ES`
-   **userId**: Required when deactivating all user tags or RFID tags
-   **tagIds**: Optional when userId is provided, required when userId is not
    provided

## Usage

-   Suitable for bulk deactivation of tags (hundreds to thousands per
    invocation).
-   Ensure Lambda memory and timeout are set high (e.g., 512MB, 900s) for large
    batches.
-   Input: a single `DeactivateTagEvent` or an array of such events.

**Examples:**

Valid input with multiple tag types:

```json
[
    {
        "userId": "user123",
        "country": "NL",
        "tagIds": [
            {
                "tagId": "123",
                "tagCardNumber": "456",
                "tagNotes": "virtual-HTB",
                "tagStatus": "ACTIVE"
            },
            {
                "tagId": "789",
                "tagCardNumber": "101112",
                "tagNotes": "virtual-DCS",
                "tagStatus": "ACTIVE"
            },
            {
                "tagId": "card-uid-123",
                "tagCardNumber": "physical-card-123",
                "tagNotes": "physical-RFID",
                "tagStatus": "ACTIVE"
            }
        ]
    }
]
```

Valid input for deactivating all user tags (tagIds will be ignored):

```json
[
    {
        "userId": "user123",
        "country": "NL",
        "tagIds": []
    }
]
```

## Performance Metrics

-   **100 records:** ~1 minute with 128MB RAM
-   **250 records:** ~30 seconds with 512MB RAM
-   **500 records:** ~1.5 minutes with 512MB RAM (peak ~250MB RAM)
-   **1000 records:** ~1.5 minutes with 512MB RAM (peak ~350MB RAM)

### Maximum Record Guidance

-   With 512MB RAM and 900s (15 min) timeout, the Lambda can process at least
    1000 records in under 2 minutes.
-   Based on observed scaling, you can expect to process up to **5000-6000
    records** per invocation within the 15-minute limit, assuming similar
    per-record processing time and memory usage.
-   For larger batches, test incrementally and monitor CloudWatch metrics for
    duration and memory.

## Handler

See `src/index.ts` for the Lambda handler implementation and documentation.

## Example Event

```json
[
    {
        "userId": "ce90ffb4-ba78-46f1-bb0d-66110f1da5b",
        "tagIds": [
            {
                "tagId": "15736",
                "tagNotes": "virtual-DCS",
                "tagCardNumber": "aeae524e-3b69-4c46-ba07-b93144fa79b",
                "tagStatus": "ACTIVE"
            }
        ],
        "country": "ES"
    }
]
```

## Sample Athena Query to Generate JSON Input

You can use Athena to generate JSON input for this Lambda as follows:

### Input with userId and country (tagIds optional)

```sql
WITH tag_data AS (
  SELECT
    u.user_authentication_id AS userId,
    ot.country,
    CONCAT(
      '{',
        '"tagId":"', CAST(t.tag_id AS varchar), '",',
        '"tagNotes":"', COALESCE(t.tag_notes, ''), '",',
        '"tagCardNumber":"', COALESCE(t.tag_card_number, ''), '",',
        '"tagStatus":"', COALESCE(t.tag_status, ''), '"',
      '}'
    ) AS tag_json
  FROM customer.users u
  INNER JOIN customer.tag t ON u.user_id = t.user_id
  LEFT JOIN customer.origin_type ot ON u.origin_type_id = ot.origin_type_id
  WHERE u.user_authentication_id IN ('user_auth_id')
    AND TRIM(LOWER(t.tag_notes)) = 'virtual-dcs'
),
user_tag_grouped AS (
  SELECT
    userId,
    country,
    '[' || array_join(array_agg(tag_json), ',') || ']' AS tag_array
  FROM tag_data
  GROUP BY userId, country
)

SELECT
  '[' ||
  array_join(
    ARRAY_AGG(
      CONCAT(
        '{',
          '"userId":"', userId, '",',
          '"country":"', COALESCE(country, ''), '",',
          '"tagIds":', tag_array,
        '}'
      )
    ),
    ','
  ) || ']' AS full_user_json
FROM user_tag_grouped;
```

-   The query above generates input for deactivation by user (requires country -
    no default value is provided).

## Logging

-   Logs batch size, processing progress, and summary statistics.
-   Errors are logged with details for troubleshooting.
-   Each deactivation attempt logs success/failure status with response details.
-   CSV format logs are generated for audit trail with unique log IDs.

### Log Verification in CloudWatch

Each Lambda execution generates a unique CSV Log ID for tracking purposes. To
verify logs and audit execution:

#### 1. Finding Logs by CSV Log ID

1. **Open CloudWatch Logs** in AWS Console
2. **Navigate to Log Groups** → `/aws/lambda/deactivateDcsHtbTags` (or your
   function name)
3. **Search for the CSV Log ID** using CloudWatch Insights or Log Events filter

#### 2. CloudWatch Insights Query Examples

**Find execution by CSV Log ID:**

```sql
fields @timestamp, @message
| filter @message like /Unique CSV Log ID: 12345678-1234-1234-1234-123456789abc/
| sort @timestamp desc
```

**Find CSV results for a specific execution:**

```sql
fields @timestamp, @message
| filter @message like /CSV_RESULT/
| filter @message like /12345678-1234-1234-1234-123456789abc/
| sort @timestamp desc
```

**Find all errors for a specific execution:**

```sql
fields @timestamp, @message
| filter @level = "ERROR"
| filter @message like /12345678-1234-1234-1234-123456789abc/
| sort @timestamp desc
```

#### 3. Log Structure

**Execution Start:**

```
[info] Unique CSV Log ID: 12345678-1234-1234-1234-123456789abc
[info] Processing batch of 5 deactivation events
```

**Processing Progress:**

```
[info] Processing event 1 of 5
[info] Completed processing event 1 with status: success
```

**Audit Trail (CSV Format):**

```
CSV_RESULT
userId,tagType,country,status,error,csvLogId
user-123,virtual-DCS,NL,success,,12345678-1234-1234-1234-123456789abc
user-456,virtual-HTB,DE,failed,Connection timeout,12345678-1234-1234-1234-123456789abc
```

#### 4. Monitoring and Alerting

-   **Set up CloudWatch Alarms** on ERROR log entries
-   **Create custom metrics** from CSV_RESULT logs for success/failure rates
-   **Use Log Insights** for detailed analysis and troubleshooting
-   **Archive logs** with the CSV Log ID for compliance and audit requirements

## Error Handling

-   **Input Validation**: Country is required and must be valid
-   **Tag Validation**: All required tag fields must be present
-   **Provider-Specific**: Different error handling for DCS, HTB, and RFID
    providers
-   **Detailed Logging**: Failed deactivations log both response status and
    error details

## Recommendations

-   For very large workloads, consider splitting invocations or using Step
    Functions for orchestration.
-   Monitor Lambda performance via CloudWatch.
-   Always provide the `country` field as it's now required (no default value).
-   When providing `userId`, the function will deactivate all eligible user tags
    and ignore the `tagIds` parameter.
-   Physical RFID tags require a valid `userId` for deactivation.
