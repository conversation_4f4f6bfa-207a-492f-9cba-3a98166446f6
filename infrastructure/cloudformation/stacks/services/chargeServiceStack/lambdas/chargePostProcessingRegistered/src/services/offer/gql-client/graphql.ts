/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
};

export type EditOfferInput = {
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  offerCode: Scalars['String']['input'];
  offerName?: InputMaybe<Scalars['String']['input']>;
  offerNotes?: InputMaybe<Scalars['String']['input']>;
  offerPublicDescription?: InputMaybe<Scalars['String']['input']>;
  partnerContribution?: InputMaybe<Scalars['Float']['input']>;
};

export type FailedOffer = {
  __typename?: 'FailedOffer';
  error: Scalars['String']['output'];
  offerCode: Scalars['String']['output'];
};

export type GenerateOffersInput = {
  creditAmount?: InputMaybe<Scalars['Float']['input']>;
  creditDuration?: InputMaybe<Scalars['Int']['input']>;
  expiryDate?: InputMaybe<Scalars['String']['input']>;
  marketingCampaignName: Scalars['String']['input'];
  offerCountry: OfferCountry;
  offerDescription: Scalars['String']['input'];
  offerPublicName: Scalars['String']['input'];
  offerType: OfferType;
  partnerCode: Scalars['String']['input'];
  partnerContribution?: InputMaybe<Scalars['Float']['input']>;
  partnerName: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  redemptionExpiryDate?: InputMaybe<Scalars['String']['input']>;
  subsDiscount?: InputMaybe<Scalars['Float']['input']>;
  subsDuration?: InputMaybe<Scalars['Int']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  applyOffer: MutationApplyOfferResult;
  editOffers: MutationEditOffersResult;
  expireOffers: MutationExpireOffersResult;
  generateOffers: MutationGenerateOffersResult;
  offerCreditStatusToUsed: MutationOfferCreditStatusToUsedResult;
  offerSubsStatusToUsed: MutationOfferSubsStatusToUsedResult;
  updateOfferCreditBalance: MutationUpdateOfferCreditBalanceResult;
};

export type MutationApplyOfferArgs = {
  offerCode: Scalars['String']['input'];
  updateWalletSubs?: InputMaybe<Scalars['Boolean']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type MutationEditOffersArgs = {
  offers: Array<EditOfferInput>;
};

export type MutationGenerateOffersArgs = {
  options: GenerateOffersInput;
};

export type MutationOfferCreditStatusToUsedArgs = {
  offerCode: Scalars['String']['input'];
};

export type MutationOfferSubsStatusToUsedArgs = {
  override?: InputMaybe<Scalars['Boolean']['input']>;
  subsPlanId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type MutationUpdateOfferCreditBalanceArgs = {
  offerCode: Scalars['String']['input'];
  usedCredit: Scalars['Float']['input'];
};

export type MutationApplyOfferResult = {
  __typename?: 'MutationApplyOfferResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type MutationEditOffersResult = {
  __typename?: 'MutationEditOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  failed?: Maybe<Array<FailedOffer>>;
  success?: Maybe<Array<Offer>>;
};

export type MutationExpireOffersResult = {
  __typename?: 'MutationExpireOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  expired?: Maybe<Array<Scalars['String']['output']>>;
  failed?: Maybe<Array<Scalars['String']['output']>>;
};

export type MutationGenerateOffersResult = {
  __typename?: 'MutationGenerateOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type MutationOfferCreditStatusToUsedResult = {
  __typename?: 'MutationOfferCreditStatusToUsedResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type MutationOfferSubsStatusToUsedResult = {
  __typename?: 'MutationOfferSubsStatusToUsedResult';
  error?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type MutationUpdateOfferCreditBalanceResult = {
  __typename?: 'MutationUpdateOfferCreditBalanceResult';
  error?: Maybe<Scalars['String']['output']>;
  offer?: Maybe<Offer>;
};

export type Offer = {
  __typename?: 'Offer';
  createdDate: Scalars['String']['output'];
  creditAmount?: Maybe<Scalars['Float']['output']>;
  creditBalance?: Maybe<Scalars['Float']['output']>;
  creditDuration?: Maybe<Scalars['Int']['output']>;
  creditStatus?: Maybe<OfferStatus>;
  currency?: Maybe<OfferCurrency>;
  expiryDate?: Maybe<Scalars['String']['output']>;
  monthsRemaining?: Maybe<Scalars['Int']['output']>;
  offerCode: Scalars['String']['output'];
  offerCountry?: Maybe<OfferCountry>;
  offerName: Scalars['String']['output'];
  offerNotes?: Maybe<Scalars['String']['output']>;
  offerPublicDescription: Scalars['String']['output'];
  offerType: OfferType;
  overrideDate?: Maybe<Scalars['String']['output']>;
  partnerContribution?: Maybe<Scalars['Float']['output']>;
  partnerName?: Maybe<Scalars['String']['output']>;
  queueStatus?: Maybe<OfferQueueStatus>;
  redemptionDate?: Maybe<Scalars['String']['output']>;
  redemptionExpiryDate?: Maybe<Scalars['String']['output']>;
  status: OfferStatus;
  subsDiscount?: Maybe<Scalars['Float']['output']>;
  subsDuration?: Maybe<Scalars['Int']['output']>;
  subsPlanId?: Maybe<Scalars['String']['output']>;
  subsStatus?: Maybe<OfferSubsStatus>;
  updatedDate: Scalars['String']['output'];
  userId?: Maybe<Scalars['String']['output']>;
};

export enum OfferCountry {
  UK = 'UK',
}

export enum OfferCurrency {
  GBP = 'GBP',
}

export enum OfferInvalidReason {
  COUNTRY_MISMATCH = 'COUNTRY_MISMATCH',
  EXCEEDED_OFFER_LIMIT = 'EXCEEDED_OFFER_LIMIT',
  EXPIRED_OFFER = 'EXPIRED_OFFER',
  OFFER_NOT_FOUND = 'OFFER_NOT_FOUND',
  PARTNER_MISMATCH = 'PARTNER_MISMATCH',
  PARTNER_RESTRICTION = 'PARTNER_RESTRICTION',
  REDEEMED_OFFER = 'REDEEMED_OFFER',
}

export enum OfferQueueStatus {
  PENDING_DELETION = 'PENDING_DELETION',
  QUEUED = 'QUEUED',
}

export enum OfferStatus {
  AVAILABLE = 'AVAILABLE',
  EXPIRED = 'EXPIRED',
  EXPIRED_REDEEM = 'EXPIRED_REDEEM',
  REDEEMED = 'REDEEMED',
  UNAVAILABLE = 'UNAVAILABLE',
  USED = 'USED',
}

export enum OfferSubsStatus {
  AVAILABLE = 'AVAILABLE',
  EXPIRED_REDEEM = 'EXPIRED_REDEEM',
  REDEEMED = 'REDEEMED',
  USED = 'USED',
}

export enum OfferType {
  COMBO = 'COMBO',
  CREDIT = 'CREDIT',
  SUBS = 'SUBS',
}

export type Query = {
  __typename?: 'Query';
  getAllOffers: QueryGetAllOffersResult;
  getOffersByUser: QueryGetOffersByUserResult;
  validateOffer: QueryValidateOfferResult;
};

export type QueryGetAllOffersArgs = {
  endDate: Scalars['String']['input'];
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  startDate: Scalars['String']['input'];
};

export type QueryGetOffersByUserArgs = {
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
  offerType?: InputMaybe<Array<OfferType>>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<UserOffersOrder>;
  status?: InputMaybe<Array<OfferStatus>>;
  userId: Scalars['String']['input'];
};

export type QueryValidateOfferArgs = {
  offerCode: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type QueryGetAllOffersResult = {
  __typename?: 'QueryGetAllOffersResult';
  error?: Maybe<Scalars['String']['output']>;
  lastEvaluatedKey?: Maybe<Scalars['String']['output']>;
  offers?: Maybe<Array<Offer>>;
};

export type QueryGetOffersByUserResult = {
  __typename?: 'QueryGetOffersByUserResult';
  error?: Maybe<Scalars['String']['output']>;
  lastEvaluatedKey?: Maybe<Scalars['String']['output']>;
  maxOfferQuantity?: Maybe<Scalars['Int']['output']>;
  maxOfferQuantityExceeded?: Maybe<Scalars['Boolean']['output']>;
  offers?: Maybe<Array<Offer>>;
  trialOffer?: Maybe<TrialOffer>;
};

export type QueryValidateOfferResult = {
  __typename?: 'QueryValidateOfferResult';
  contradictoryOffer?: Maybe<Offer>;
  error?: Maybe<Scalars['String']['output']>;
  isValid?: Maybe<Scalars['Boolean']['output']>;
  offer?: Maybe<Offer>;
  reason?: Maybe<OfferInvalidReason>;
};

export type TrialOffer = {
  __typename?: 'TrialOffer';
  planDuration?: Maybe<Scalars['Int']['output']>;
  trialBillingDate?: Maybe<Scalars['String']['output']>;
  trialRedemptionDate?: Maybe<Scalars['String']['output']>;
  trialStatus: OfferStatus;
};

export enum UserOffersOrder {
  EXPIRY_DATE = 'EXPIRY_DATE',
  UPDATE_DATE = 'UPDATE_DATE',
}

export type UpdateOfferCreditBalanceMutationVariables = Exact<{
  offerCode: Scalars['String']['input'];
  usedCredit: Scalars['Float']['input'];
}>;

export type UpdateOfferCreditBalanceMutation = {
  __typename?: 'Mutation';
  updateOfferCreditBalance: {
    __typename?: 'MutationUpdateOfferCreditBalanceResult';
    error?: string | null;
    offer?: {
      __typename?: 'Offer';
      offerCode: string;
      creditBalance?: number | null;
      currency?: OfferCurrency | null;
    } | null;
  };
};

export type GetOffersByUserQueryVariables = Exact<{
  userId: Scalars['String']['input'];
  status?: InputMaybe<Array<OfferStatus> | OfferStatus>;
  offerType?: InputMaybe<Array<OfferType> | OfferType>;
  sortBy?: InputMaybe<UserOffersOrder>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  lastEvaluatedKey?: InputMaybe<Scalars['String']['input']>;
}>;

export type GetOffersByUserQuery = {
  __typename?: 'Query';
  getOffersByUser: {
    __typename?: 'QueryGetOffersByUserResult';
    error?: string | null;
    offers?: Array<{
      __typename?: 'Offer';
      offerCode: string;
      creditBalance?: number | null;
      currency?: OfferCurrency | null;
      creditStatus?: OfferStatus | null;
    }> | null;
  };
};

export const UpdateOfferCreditBalanceDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: { kind: 'Name', value: 'updateOfferCreditBalance' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'offerCode' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'usedCredit' },
          },
          type: {
            kind: 'NonNullType',
            type: { kind: 'NamedType', name: { kind: 'Name', value: 'Float' } },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'updateOfferCreditBalance' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'offerCode' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'offerCode' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'usedCredit' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'usedCredit' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'offer' },
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'offerCode' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'creditBalance' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'currency' },
                      },
                    ],
                  },
                },
                { kind: 'Field', name: { kind: 'Name', value: 'error' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateOfferCreditBalanceMutation,
  UpdateOfferCreditBalanceMutationVariables
>;
export const GetOffersByUserDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: { kind: 'Name', value: 'getOffersByUser' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'status' },
          },
          type: {
            kind: 'ListType',
            type: {
              kind: 'NonNullType',
              type: {
                kind: 'NamedType',
                name: { kind: 'Name', value: 'OfferStatus' },
              },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'offerType' },
          },
          type: {
            kind: 'ListType',
            type: {
              kind: 'NonNullType',
              type: {
                kind: 'NamedType',
                name: { kind: 'Name', value: 'OfferType' },
              },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'sortBy' },
          },
          type: {
            kind: 'NamedType',
            name: { kind: 'Name', value: 'UserOffersOrder' },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'pageSize' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'lastEvaluatedKey' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'getOffersByUser' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'userId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'userId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'status' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'status' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'offerType' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'offerType' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'sortBy' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'sortBy' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'pageSize' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'pageSize' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'lastEvaluatedKey' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'lastEvaluatedKey' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'offers' },
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'offerCode' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'creditBalance' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'currency' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'creditStatus' },
                      },
                    ],
                  },
                },
                { kind: 'Field', name: { kind: 'Name', value: 'error' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetOffersByUserQuery,
  GetOffersByUserQueryVariables
>;
