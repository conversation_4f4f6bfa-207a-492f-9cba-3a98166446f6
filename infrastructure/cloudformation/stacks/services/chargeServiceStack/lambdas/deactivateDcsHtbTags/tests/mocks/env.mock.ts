// Mock environment variables for tests
export const env = {
    DCS_TOKEN_URL: 'https://mock-dcs-token-url',
    DCS_TOKEN_CLIENT_ID: 'mock-client-id',
    DCS_TOKEN_CLIENT_SECRET: 'mock-client-secret',
    DCS_TOKEN_RESOURCE: 'mock-resource',
    DCS_SUBSCRIPTION_KEY: 'mock-subscription-key',
    DCS_FLEET_SERVICES_URL: 'https://mock-dcs-fleet-url',
    DCS_FLEET_ACCESS_KEY: 'mock-access-key',
    DCS_FLEET_GROUP: 'MOCKGROUP',
    NL_FLEET_GROUP: 'MOCKGROUP_NL',
    DE_FLEET_GROUP: 'MOC<PERSON><PERSON>OUP_DE',
    ES_FLEET_GROUP: 'MOCKGROUP_ES',
    HTB_SERVICES_REST: 'https://mock-htb-rest',
    HTB_SERVICES_AUTH: 'https://mock-htb-auth',
    HTB_X_API_KEY: 'mock-api-key',
    HTB_AUTH_TOKEN: 'mock-auth-token',
    HTB_SERVICES_USERNAME_NL: 'mock-username-nl',
    HTB_SERVICES_PASSWORD_NL: 'mock-password-nl',
    HTB_SERVICES_USERNAME_DE: 'mock-username-de',
    HTB_SERVICES_PASSWORD_DE: 'mock-password-de',
    HTB_SERVICES_USERNAME_ES: 'mock-username-es',
    HTB_SERVICES_PASSWORD_ES: 'mock-password-es',
    PRIVATE_GATEWAY_SERVER_HTTP: 'https://mock-private-gateway',
    APOLLO_INTERNAL_SECRET: 'mock-internal-secret',
    RFID_API_URL: 'https://mock-rfid-api',
};
