import { Countries, TagNotes, TagStatus } from '../common/enums';
import { Providers, TagId } from '../common/interfaces';
import logger from '../utils/logger';
import { hasInvalidTagStatus, processAndUpdateRFID } from './rfidHelpers';

const mockShouldProcessHTB = jest.fn();
const mockUpdatePhysicalAuthMediaHtb = jest.fn();

jest.mock('../providers/aurora/aurora', () => ({
  shouldProcessHASTOBE: jest.fn((...args: unknown[]) =>
    mockShouldProcessHTB(...args),
  ),
}));

jest.mock('../services/htb', () => ({
  updatePhysicalAuthMediaHtb: jest.fn((...args: unknown[]) =>
    mockUpdatePhysicalAuthMediaHtb(...args),
  ),
}));

jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
}));

const NO_TAG_MESSAGE = 'No tag matching the specified criteria was found';
const UPDATE_MESSAGE = 'Successfully updated RFID';

const TAG_ID = 'DE*DCS*00A6RB*7';
const TAG_CARD_NUMBER = '55271c96-b8c5-4c5f-9b0d-3d58ee6112ea';
const HTB_TEST_PLAN = 'HTB Test Plan';

describe('hasInvalidTagStatus', () => {
  it('should return false for ACTIVE', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.ACTIVE);
    expect(isInvalidStatus).toBeFalsy();
  });

  it('should return false for NO_CREDIT', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.NO_CREDIT);
    expect(isInvalidStatus).toBeFalsy();
  });

  it('should return false for BLOCKED', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.BLOCKED);
    expect(isInvalidStatus).toBeFalsy();
  });

  it('should return true for PENDING_TERMINATION', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.PENDING_TERMINATION);
    expect(isInvalidStatus).toBeTruthy();
  });

  it('should return true for SUPPLIER_REQUESTED', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.SUPPLIER_REQUESTED);
    expect(isInvalidStatus).toBeTruthy();
  });

  it('should return true for CUSTOMER_REQUESTED', () => {
    const isInvalidStatus = hasInvalidTagStatus(TagStatus.CUSTOMER_REQUESTED);
    expect(isInvalidStatus).toBeTruthy();
  });
});

describe('processAndUpdateRFID', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    delete process.env.ENABLE_HTB_UK;
  });

  afterEach(() => {
    delete process.env.ENABLE_HTB_UK;
  });

  const createValidTagsArray = () => [
    {
      tagInternalId: 1,
      tagId: TAG_ID,
      tagCardNumber: TAG_CARD_NUMBER,
      tagTypeName: 'physical',
      tagCategoryName: 'RFID',
      tagStatus: TagStatus.ACTIVE,
      tagNotes: TagNotes.PHYSICAL_RFID,
    },
    {
      tagInternalId: 2,
      tagId: 'DE*DCS*00A6RB*8',
      tagCardNumber: '55271c96-b8c5-4c5f-9b0d-3d58ee6112eb',
      tagTypeName: 'physical',
      tagCategoryName: 'RFID',
      tagStatus: TagStatus.NO_CREDIT,
      tagNotes: TagNotes.PHYSICAL_RFID,
    },
  ];

  const createRevenuePlans = () => [
    {
      provider: 'HTB',
      revenuePlanName: HTB_TEST_PLAN,
    },
  ];

  describe('UK Country with ENABLE_HTB_UK Feature Flag', () => {
    it('should process HTB for UK when ENABLE_HTB_UK is true', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(2);

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        TAG_ID,
        '1',
        TAG_CARD_NUMBER,
        '12345',
        undefined,
        HTB_TEST_PLAN,
      );

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        'DE*DCS*00A6RB*8',
        '0',
        '55271c96-b8c5-4c5f-9b0d-3d58ee6112eb',
        '12345',
        undefined,
        HTB_TEST_PLAN,
      );

      expect(logger.info).toHaveBeenCalledWith(
        `12345 - ${UPDATE_MESSAGE}: DE*DCS*00A6RB*7`,
      );
      expect(logger.info).toHaveBeenCalledWith(
        `12345 - ${UPDATE_MESSAGE}: DE*DCS*00A6RB*8`,
      );

      expect(result).toEqual({
        message: UPDATE_MESSAGE,
        status: 200,
      });
    });

    it('should skip HTB processing for UK when ENABLE_HTB_UK is false', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockShouldProcessHTB.mockReturnValue(false);

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Valid RFID tag found DE*DCS*00A6RB*7',
      );
      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Valid RFID tag found DE*DCS*00A6RB*8',
      );

      expect(result).toEqual({
        message: NO_TAG_MESSAGE,
        status: 500,
      });
    });

    it('should skip HTB processing for UK when ENABLE_HTB_UK is undefined', async () => {
      delete process.env.ENABLE_HTB_UK;
      mockShouldProcessHTB.mockReturnValue(false);

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(result).toEqual({
        message: NO_TAG_MESSAGE,
        status: 500,
      });
    });
  });

  describe('Non-UK Countries (should always process HTB regardless of feature flag)', () => {
    it('should always process HTB for DE regardless of ENABLE_HTB_UK setting', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.DE,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.DE);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(2);

      expect(result).toEqual({
        message: UPDATE_MESSAGE,
        status: 200,
      });
    });

    it('should always process HTB for NL regardless of ENABLE_HTB_UK setting', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.NL,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.NL);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(2);

      expect(result).toEqual({
        message: UPDATE_MESSAGE,
        status: 200,
      });
    });
  });

  describe('HTB Processing Behavior with Mixed Valid/Invalid Tags', () => {
    it('should only process valid tags when ENABLE_HTB_UK is true for UK', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = [
        {
          tagInternalId: 1,
          tagId: TAG_ID,
          tagCardNumber: TAG_CARD_NUMBER,
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagStatus: TagStatus.ACTIVE,
          tagNotes: TagNotes.PHYSICAL_RFID,
        },
        {
          tagInternalId: 2,
          tagId: null,
          tagCardNumber: 'DEBPECNNN1161',
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagStatus: TagStatus.ACTIVE,
          tagNotes: TagNotes.PHYSICAL_RFID,
        },
        {
          tagInternalId: 3,
          tagId: 'DEHTBD634D2CDB265E87',
          tagCardNumber: 'DEHTBD634D2CDB265E87',
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagStatus: TagStatus.PENDING_TERMINATION,
          tagNotes: TagNotes.PHYSICAL_RFID,
        },
      ];

      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);

      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(1);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
        TAG_ID,
        '1',
        TAG_CARD_NUMBER,
        '12345',
        undefined,
        HTB_TEST_PLAN,
      );

      expect(logger.info).toHaveBeenCalledWith(
        '12345 - there have been 2 invalid tags (missing tag serial number or having invalid status)',
      );

      expect(result).toEqual({
        message: UPDATE_MESSAGE,
        status: 200,
      });
    });

    it('should skip all HTB processing when ENABLE_HTB_UK is false for UK, even with valid tags', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockShouldProcessHTB.mockReturnValue(false);

      const arrayOfTags = [
        {
          tagInternalId: 1,
          tagId: TAG_ID,
          tagCardNumber: TAG_CARD_NUMBER,
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagStatus: TagStatus.ACTIVE,
          tagNotes: TagNotes.PHYSICAL_RFID,
        },
        {
          tagInternalId: 2,
          tagId: null,
          tagCardNumber: 'DEBPECNNN1161',
          tagTypeName: 'physical',
          tagCategoryName: 'RFID',
          tagStatus: TagStatus.ACTIVE,
          tagNotes: TagNotes.PHYSICAL_RFID,
        },
      ];

      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Valid RFID tag found DE*DCS*00A6RB*7',
      );

      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Missing tag serial number for card',
        'DEBPECNNN1161',
      );

      expect(result).toEqual({
        message: NO_TAG_MESSAGE,
        status: 500,
      });
    });
  });

  describe('HTB API Failure Scenarios', () => {
    it('should handle HTB API failure when ENABLE_HTB_UK is true for UK', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 500 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(2);

      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Failed to update RFID: DE*DCS*00A6RB*7',
      );
      expect(logger.info).toHaveBeenCalledWith(
        '12345 - Failed to update RFID: DE*DCS*00A6RB*8',
      );

      expect(result).toEqual({
        status: 500,
        message:
          'Failed to update the following RFID tags: ["DE*DCS*00A6RB*7","DE*DCS*00A6RB*8"]',
      });
    });

    it('should return error when HTB is skipped for UK (no API calls made)', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockShouldProcessHTB.mockReturnValue(false);

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(result).toEqual({
        message: NO_TAG_MESSAGE,
        status: 500,
      });
    });
  });

  describe('Feature Flag Verification', () => {
    it('should handle empty tags array regardless of feature flag', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);

      const arrayOfTags: TagId[] = [];
      const revenuePlans = createRevenuePlans();

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).not.toHaveBeenCalled();
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(result).toEqual({
        status: 500,
        message: 'No physical-RFID tags available',
      });
    });

    it('should handle non-HTB providers regardless of feature flag', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = [
        {
          provider: Providers.DCS,
          revenuePlanName: 'DCS Test Plan',
        },
      ];

      const result = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).not.toHaveBeenCalled();
      expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();

      expect(result).toEqual({
        status: 500,
        message: NO_TAG_MESSAGE,
      });
    });

    it('should verify shouldProcessHASTOBE is called with correct country parameter', async () => {
      process.env.ENABLE_HTB_UK = 'true';
      mockShouldProcessHTB.mockReturnValue(true);
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.DE,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.DE);
      expect(mockShouldProcessHTB).toHaveBeenCalledTimes(2);
    });

    it('should demonstrate different behavior between UK and non-UK when feature flag is disabled', async () => {
      process.env.ENABLE_HTB_UK = 'false';
      mockUpdatePhysicalAuthMediaHtb.mockResolvedValue({ status: 200 });

      const arrayOfTags = createValidTagsArray();
      const revenuePlans = createRevenuePlans();

      mockShouldProcessHTB.mockReturnValue(false);
      const ukResult = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.UK,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.UK);
      expect(ukResult.status).toBe(500);

      jest.clearAllMocks();

      mockShouldProcessHTB.mockReturnValue(true);
      const deResult = await processAndUpdateRFID(
        arrayOfTags,
        revenuePlans,
        '12345',
        Countries.DE,
      );

      expect(mockShouldProcessHTB).toHaveBeenCalledWith(Countries.DE);
      expect(deResult.status).toBe(200);
      expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledTimes(2);
    });
  });
});
