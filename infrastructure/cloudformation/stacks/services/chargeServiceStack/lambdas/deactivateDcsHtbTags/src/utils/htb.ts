import axios, { AxiosResponse } from 'axios';
import { env } from '../env';
import { GetHTBTokenResponse } from '../types/types';
import { DeactivateHTBCardInput } from './constants';
import { getHTBCredentials } from './helpers';
import { logger } from './logger';

const htbTokenCache = new Map<string, string>();

export const getHTBToken = async ({
    username,
    password,
}: {
    username: string;
    password: string;
}): Promise<AxiosResponse<GetHTBTokenResponse>> =>
    axios
        .post(
            `${env.HTB_SERVICES_AUTH}`,
            {
                query: `mutation initSession($username: String!, $password: String!)
            {initSession(username: $username, password: $password) {sessionToken, refreshToken}}
        `,
                variables: {
                    username,
                    password,
                },
            },
            {
                headers: {
                    Authorization: `Bearer ${env.HTB_AUTH_TOKEN}`,
                },
            },
        )
        .catch((err) => {
            console.error(
                `🚨 Error encountered: failed to get htb auth token. ${JSON.stringify(
                    err,
                )}`,
                err,
            );
            throw new Error(`failed to update htb auth token. ${err}`);
        });

async function getOrFetchHTBToken(country: string): Promise<string> {
    if (htbTokenCache.has(country)) {
        return htbTokenCache.get(country)!;
    }
    const creds = getHTBCredentials({ country });
    const response = await getHTBToken(creds);
    const token = response?.data?.data?.initSession?.sessionToken;
    if (!token) throw new Error(`No HTB token received for country ${country}`);
    htbTokenCache.set(country, token);
    return token;
}

export const deactivateHTBCard = async ({
    serialNumber,
    cardNumber,
    country,
}: DeactivateHTBCardInput) => {
    let htbSessionToken: string;
    try {
        htbSessionToken = await getOrFetchHTBToken(country);
    } catch (error: any) {
        logger.error(
            `Retrieving the HTB token failed with the following error: ${error}`,
        );
        return {
            status: 500,
            message: 'There was an error getting HTB token',
            response: error.message,
        };
    }
    if (!htbSessionToken) {
        return {
            status: 500,
            message: 'Could not parse HTB access token',
            response: null,
        };
    }
    return axios
        .put(
            `${env.HTB_SERVICES_REST}/card`,
            {
                tag: serialNumber,
                number: cardNumber,
                active: '0',
                reference: 'bp_pulse',
                expires: new Date().toISOString().split('T')[0],
            },
            {
                headers: {
                    'x-api-token': env.HTB_X_API_KEY,
                    Authorization: `Bearer ${htbSessionToken}`,
                },
            },
        )
        .then(async (response) => {
            if (response.data.status === 'ok' || response.status === 200) {
                const htbCard = await axios.get(
                    `${env.HTB_SERVICES_REST}/card/${serialNumber}`,
                    {
                        headers: {
                            'x-api-token': env.HTB_X_API_KEY,
                            Authorization: `Bearer ${htbSessionToken}`,
                        },
                    },
                );
                logger.info(
                    `The HTB card (${serialNumber}) has been found: ${JSON.stringify(
                        htbCard.data,
                    )}`,
                );
                if (!htbCard.data.active) {
                    return {
                        status: 200,
                        message: `The HTB Card ${serialNumber} has been successfully deactivated`,
                    };
                } else {
                    return {
                        status: 500,
                        message: `The HTB Card ${serialNumber} has not been deactivated`,
                        data: htbCard.data,
                    };
                }
            } else {
                return { status: 500, message: response.data };
            }
        })
        .catch((error) => {
            console.error(
                `🚨 Error encountered: failed to update htb card. ${JSON.stringify(
                    error,
                )}`,
                error,
            );
            throw new Error(`failed to update htb card. ${error}`);
        });
};
