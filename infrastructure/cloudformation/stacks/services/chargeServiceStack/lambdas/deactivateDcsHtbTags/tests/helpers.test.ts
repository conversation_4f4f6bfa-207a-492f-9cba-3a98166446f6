import { getFleetGroup, getHTBCredentials } from '../src/utils/helpers';
import { Countries } from '../src/utils/constants';

jest.mock('../src/env', () => ({
    env: {
        NL_FLEET_GROUP: 'nl-fleet',
        DE_FLEET_GROUP: 'de-fleet',
        ES_FLEET_GROUP: 'es-fleet',
        HTB_SERVICES_USERNAME_NL: 'nl-username',
        HTB_SERVICES_PASSWORD_NL: 'nl-password',
        HTB_SERVICES_USERNAME_DE: 'de-username',
        HTB_SERVICES_PASSWORD_DE: 'de-password',
        HTB_SERVICES_USERNAME_ES: 'es-username',
        HTB_SERVICES_PASSWORD_ES: 'es-password',
    },
}));

describe('Helper Utilities', () => {
    describe('getFleetGroup', () => {
        it('should return correct fleet group for NL', () => {
            const result = getFleetGroup({ country: Countries.NL });
            expect(result).toBe('nl-fleet');
        });

        it('should return correct fleet group for DE', () => {
            const result = getFleetGroup({ country: Countries.DE });
            expect(result).toBe('de-fleet');
        });

        it('should return correct fleet group for ES', () => {
            const result = getFleetGroup({ country: Countries.ES });
            expect(result).toBe('es-fleet');
        });

        it('should return empty string for unsupported country', () => {
            const result = getFleetGroup({ country: 'FR' });
            expect(result).toBe('');
        });
    });

    describe('getHTBCredentials', () => {
        it('should return correct credentials for NL', () => {
            const result = getHTBCredentials({ country: Countries.NL });
            expect(result).toEqual({
                username: 'nl-username',
                password: 'nl-password',
            });
        });

        it('should return correct credentials for DE', () => {
            const result = getHTBCredentials({ country: Countries.DE });
            expect(result).toEqual({
                username: 'de-username',
                password: 'de-password',
            });
        });

        it('should return correct credentials for ES', () => {
            const result = getHTBCredentials({ country: Countries.ES });
            expect(result).toEqual({
                username: 'es-username',
                password: 'es-password',
            });
        });

        it('should return empty credentials for unsupported country', () => {
            const result = getHTBCredentials({ country: 'FR' });
            expect(result).toEqual({
                username: '',
                password: '',
            });
        });
    });
});
