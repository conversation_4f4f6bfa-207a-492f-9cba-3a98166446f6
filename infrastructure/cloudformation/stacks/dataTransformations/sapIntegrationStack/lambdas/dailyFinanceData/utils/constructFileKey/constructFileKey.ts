import { PROVIDER } from '../../common/enums';
import { formatDate } from '../formatDate/formatDate';

enum EMSP_PATH {
  DCS = 'Sales_invoice/Invoices/DCS/upload_date=',
  BPCM = 'Sales_invoice/Invoices/BPCM/upload_date=',
  HASTOBE = 'Sales_invoice/Invoices/HTB/upload_date=',
}

enum FILE_NAME {
  DCS = 'EMSP_DCS_INV_',
  BPCM = 'EMSP_BPCM_INV_',
  HASTOBE = 'EMSP_HTB_INV_',
}

enum DATA_HUB_PATH {
  DCS = 'apollo/emsp/daily_sales_invoices/DCS',
  BPCM = 'apollo/emsp/daily_sales_invoices/BPCM',
  HASTOBE = 'apollo/emsp/daily_sales_invoices/HTB',
}

type constractKeyResponse = { dataHubKey: string; emspKey: string };

export const constructFileKey = (
  provider: PROVIDER,
  startDate: string,
  endDate: string,
): constractKeyResponse => {
  if (!Object.values(PROVIDER).includes(provider)) {
    throw new Error(
      `Invalid provider in file key construction process: ${provider}`,
    );
  }

  const fileDate = new Date(startDate);
  const pathDate = new Date(endDate);

  if (isNaN(fileDate.getTime())) {
    throw new Error(
      `Invalid date for file key construction process: ${fileDate}`,
    );
  }

  if (isNaN(pathDate.getTime())) {
    throw new Error(`Invalid date for path construction process: ${pathDate}`);
  }

  const emspPath = EMSP_PATH[provider];
  const dataHubPath = DATA_HUB_PATH[provider];
  const fileName = FILE_NAME[provider];

  const pathDateDataHub = `${pathDate.getFullYear()}/${String(
    pathDate.getMonth() + 1,
  ).padStart(2, '0')}/${String(pathDate.getDate()).padStart(2, '0')}`;
  const fileDateDataHub = formatDate(fileDate, '');
  const dataHubKey = `${dataHubPath}/${pathDateDataHub}/${fileName}${fileDateDataHub}.csv.gz`;

  const pathDateEmsp = formatDate(pathDate, '-');
  const fileDateEmsp = formatDate(fileDate, '');
  const emspKey = `${emspPath}${pathDateEmsp}/${fileName}${fileDateEmsp}.csv`;

  return {
    dataHubKey,
    emspKey,
  };
};
