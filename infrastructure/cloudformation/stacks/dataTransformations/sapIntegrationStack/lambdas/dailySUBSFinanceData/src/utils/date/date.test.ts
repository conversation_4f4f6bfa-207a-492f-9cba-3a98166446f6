import { getYesterdayStart, getTodayStart, getTodaysDay } from './date';

describe('getYesterdayStart', () => {
  it('should return the start of yesterday at 00:00:00.000 UTC', () => {
    const now = new Date('2024-06-15T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => now as any);

    const expected = new Date('2024-06-14T00:00:00.000Z');
    const result = getYesterdayStart();

    expect(result.toISOString()).toBe(expected.toISOString());

    jest.spyOn(global, 'Date').mockRestore();
  });
});

describe('getTodayStart', () => {
  it('should return the start of today at 00:00:00.000 UTC', () => {
    const now = new Date('2024-06-15T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => now as any);

    const expected = new Date('2024-06-15T00:00:00.000Z');
    const result = getTodayStart();

    expect(result.toISOString()).toBe(expected.toISOString());

    jest.spyOn(global, 'Date').mockRestore();
  });

  it('should handle different times of day correctly', () => {
    // Most commonly mistaken scenario 23:59:59
    const lateNight = new Date('2024-06-15T23:59:59Z');
    jest.spyOn(global, 'Date').mockImplementation(() => lateNight as any);

    const expectedLate = new Date('2024-06-15T00:00:00.000Z');
    const resultLate = getTodayStart();

    expect(resultLate.toISOString()).toBe(expectedLate.toISOString());

    // Possible scenario of 00:00:01
    const earlyMorning = new Date('2024-06-15T00:00:01Z');
    jest.spyOn(global, 'Date').mockImplementation(() => earlyMorning as any);

    const expectedEarly = new Date('2024-06-15T00:00:00.000Z');
    const resultEarly = getTodayStart();

    expect(resultEarly.toISOString()).toBe(expectedEarly.toISOString());

    jest.spyOn(global, 'Date').mockRestore();
  });
});

describe('getTodaysDay', () => {
  it('should return the current day as a two-digit string', () => {
    const now = new Date('2024-06-15T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => now as any);

    const result = getTodaysDay();

    expect(result).toBe('15');

    jest.spyOn(global, 'Date').mockRestore();
  });

  it('should return single digit with 0 prefixed', () => {
    const now = new Date('2024-06-01T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => now as any);

    const result = getTodaysDay();

    expect(result).toBe('01');

    jest.spyOn(global, 'Date').mockRestore();
  });

  it('should not return just single digit', () => {
    const now = new Date('2024-06-02T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => now as any);

    const result = getTodaysDay();

    expect(result).not.toBe('2');
    expect(result).toBe('02');

    jest.spyOn(global, 'Date').mockRestore();
  });
});
