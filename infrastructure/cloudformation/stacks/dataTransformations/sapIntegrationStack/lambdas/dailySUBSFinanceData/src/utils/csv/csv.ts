import { Readable } from 'node:stream';
import { promisify } from 'node:util';
import { gzip } from 'node:zlib';

import { stringify } from 'csv-stringify';
import { DATA_HUB_BUCKET, DE_SUBS_FINANCE_SUPPORT } from '../../env/env';
import {
  Columns,
  MergedSubscriptionRecord,
} from '../../models/SubsTransaction';
import { put } from '../../services/s3/s3';
import { logger } from '../logger/logger';
import { round2dp } from '../transformations/dataMerge';
// The bucket name is only for testing purposes; change if required
const BUCKET_NAME_DATAHUB =
  DATA_HUB_BUCKET || 'ws-00f0-naglis-testing-bucket-pr';

const gzipPromise = promisify(gzip);

const REQUIRED_COLUMNS = {
  USER_ID: 'USER_ID',
  INVOICE_ID: 'INVOICE_ID',
  INVOICE_DATE: 'INVOICE_DATE',
  PRICE_BASE: 'PRICE_BASE',
  PRICE_NET: 'PRICE_NET',
  PRICE_VAT: 'PRICE_VAT',
  PRICE_TAX_RATE: 'PRICE_TAX_RATE',
  PRICE_DISCOUNT_GROSS: 'PRICE_DISCOUNT_GROSS',
  PRICE_DISCOUNT_NET: 'PRICE_DISCOUNT_NET',
  PRICE_GROSS: 'PRICE_GROSS',
  TRANSACTION_ID: 'TRANSACTION_ID',
  BILLING_CYCLE_DATE: 'BILLING_CYCLE_DATE',
  USER_HOME_COUNTRY: 'USER_HOME_COUNTRY',
  PARTNER_TYPE: 'PARTNER_TYPE',
};

const DE_SPECIFIC_COLUMNS = {
  INVOICE_GEN_DATE: 'INVOICE_GEN_DATE',
  SUB_ID: 'SUB_ID',
  ACQUIRER: 'ACQUIRER',
  RRN: 'RRN',
  TRX_CURRENCY: 'TRX_CURRENCY',
  IS_TRIAL: 'IS_TRIAL',
};

const getColumns = () => ({
  ...REQUIRED_COLUMNS,
  ...(DE_SUBS_FINANCE_SUPPORT === 'true' ? DE_SPECIFIC_COLUMNS : {}),
});

const getCorrectPriceValue = (record: MergedSubscriptionRecord) =>
  record.isTrial
    ? record.priceDetails.defaultFee
    : record.priceDetails.priceGross;

const transformRecord = (record: MergedSubscriptionRecord) => {
  const requiredColumns = {
    [REQUIRED_COLUMNS.USER_ID]: record.userId,
    [REQUIRED_COLUMNS.INVOICE_ID]: record.receipt_id,
    [REQUIRED_COLUMNS.INVOICE_DATE]: record.transactionDate,
    [REQUIRED_COLUMNS.PRICE_BASE]: record.priceDetails.priceBase,
    [REQUIRED_COLUMNS.PRICE_NET]: record.priceDetails.priceNet,
    [REQUIRED_COLUMNS.PRICE_VAT]: record.priceDetails.priceVAT,
    [REQUIRED_COLUMNS.PRICE_TAX_RATE]: record.priceDetails.priceTaxRate,
    [REQUIRED_COLUMNS.PRICE_DISCOUNT_GROSS]: round2dp(
      record.priceDetails.priceDiscount,
    ),
    [REQUIRED_COLUMNS.PRICE_DISCOUNT_NET]: 0,
    [REQUIRED_COLUMNS.PRICE_GROSS]: getCorrectPriceValue(record),
    [REQUIRED_COLUMNS.TRANSACTION_ID]: record.transactionId,
    [REQUIRED_COLUMNS.BILLING_CYCLE_DATE]: record.nextBillingCycleDate,
    [REQUIRED_COLUMNS.USER_HOME_COUNTRY]: record.country,
    [REQUIRED_COLUMNS.PARTNER_TYPE]: record.partnerType,
  };
  const deSubsSpecificColumns = {
    [DE_SPECIFIC_COLUMNS.INVOICE_GEN_DATE]: record.salesPostingDate
      ? Math.floor(record.salesPostingDate / 1000)
      : '',
    [DE_SPECIFIC_COLUMNS.SUB_ID]: record.subsAccountId,
    [DE_SPECIFIC_COLUMNS.ACQUIRER]: record.acquirer,
    [DE_SPECIFIC_COLUMNS.RRN]: record.retrievalReferenceNumber,
    [DE_SPECIFIC_COLUMNS.TRX_CURRENCY]: record.priceDetails.currency,
    [DE_SPECIFIC_COLUMNS.IS_TRIAL]: record.isTrial ? 'TRUE' : 'FALSE',
  };

  return {
    ...requiredColumns,
    ...(DE_SUBS_FINANCE_SUPPORT === 'true' ? deSubsSpecificColumns : {}),
  };
};

export const extractYearMonth = (dateString: string): string => {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) throw new Error('Invalid date string');

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');

  return `${year}-${month}`;
};

export const extractYearMonthDay = (dateString: string): string => {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) throw new Error('Invalid date string');

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const createCSVStream = async (data: MergedSubscriptionRecord[]) => {
  const columns: Columns = getColumns();

  const csvStream = stringify({
    header: true,
    columns: Object.values(columns),
  });

  for (const record of data) csvStream.write(transformRecord(record));
  csvStream.end();

  const buffers: Buffer[] = [];
  for await (const chunk of csvStream) buffers.push(Buffer.from(chunk));
  const csvBuffer = Buffer.concat(buffers);

  const gzipped = await gzipPromise(csvBuffer);
  const uploadStream = new Readable({ read() {} });
  uploadStream.push(gzipped);
  uploadStream.push(null);

  return { stream: uploadStream, length: gzipped.length };
};

export const uploadCSV = async (
  subsTransactions: MergedSubscriptionRecord[],
  startDate: Date,
  endDate: Date,
) => {
  const csvStreamObj = await createCSVStream(subsTransactions);

  // Filename based on batch start date
  const startYear = startDate.getFullYear();
  const startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
  const startDay = String(startDate.getDate()).padStart(2, '0');
  const fileName = `SUBS_INV_${startYear}${startMonth}${startDay}.csv.gz`;

  // Path partition based on endDate
  const pathDate = new Date(endDate);
  pathDate.setUTCHours(0, 0, 0, 0);

  const pathYear = pathDate.getFullYear();
  const pathMonth = String(pathDate.getMonth() + 1).padStart(2, '0');
  const pathDay = String(pathDate.getDate()).padStart(2, '0');

  const key = `apollo/emsp/daily_paid_subscriptions/${pathYear}/${pathMonth}/${pathDay}/${fileName}`;

  logger.info('Putting file in path:', key);
  logger.info(
    `Path date: ${pathDate.toISOString()}, File date: ${startDate.toISOString()}`,
  );

  return put({ bucketName: BUCKET_NAME_DATAHUB, key, bodyObj: csvStreamObj });
};
