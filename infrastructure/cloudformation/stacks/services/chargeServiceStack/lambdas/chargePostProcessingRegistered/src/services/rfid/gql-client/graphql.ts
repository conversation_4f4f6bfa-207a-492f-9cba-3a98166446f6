/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
};

export type AddRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type AddRfidResponse = {
  __typename?: 'AddRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type BlockRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  reasonForBlocking: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type BlockRfidResponse = {
  __typename?: 'BlockRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type DeleteRfidRecordResponse = {
  __typename?: 'DeleteRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type DeleteRfidParamsObject = {
  cardNumber: Scalars['String']['input'];
};

export type DeprecatedPaymentRfidResponse = {
  __typename?: 'DeprecatedPaymentRFIDResponse';
  data?: Maybe<DeprecatedPaymentRfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type DeprecatedPaymentRfidResponseData = {
  __typename?: 'DeprecatedPaymentRFIDResponseData';
  event_details?: Maybe<Scalars['String']['output']>;
  event_time?: Maybe<Scalars['String']['output']>;
  salesforce_ID?: Maybe<Scalars['String']['output']>;
};

export type ExistingRfidRecordResponse = {
  __typename?: 'ExistingRFIDRecordResponse';
  data?: Maybe<Array<Maybe<ExistingRfidRecordResponseData>>>;
};

export type ExistingRfidRecordResponseData = {
  __typename?: 'ExistingRFIDRecordResponseData';
  additional_address_1?: Maybe<Scalars['String']['output']>;
  additional_address_2?: Maybe<Scalars['String']['output']>;
  address_city?: Maybe<Scalars['String']['output']>;
  address_country?: Maybe<Scalars['String']['output']>;
  address_line?: Maybe<Scalars['String']['output']>;
  address_postcode?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  date_added?: Maybe<Scalars['String']['output']>;
  first_name?: Maybe<Scalars['String']['output']>;
  last_name?: Maybe<Scalars['String']['output']>;
  partner_type?: Maybe<Scalars['String']['output']>;
  rfid_card_number?: Maybe<Scalars['String']['output']>;
  rfid_status?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type GetLatestRequestIdentifierResponse = {
  __typename?: 'GetLatestRequestIdentifierResponse';
  latestRequestIdentifier?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type GetRfidParamsObjectCard = {
  cardNumber: Scalars['String']['input'];
  exclusiveStartKey?: InputMaybe<Scalars['String']['input']>;
};

export type GetRfidParamsObjectStatus = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  exclusiveStartKey?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  status: Scalars['String']['input'];
};

export type InsertItem = {
  additional_address_1?: InputMaybe<Scalars['String']['input']>;
  additional_address_2?: InputMaybe<Scalars['String']['input']>;
  address_city?: InputMaybe<Scalars['String']['input']>;
  address_country?: InputMaybe<Scalars['String']['input']>;
  address_line?: InputMaybe<Scalars['String']['input']>;
  address_postcode?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  date_added?: InputMaybe<Scalars['String']['input']>;
  first_name?: InputMaybe<Scalars['String']['input']>;
  last_name?: InputMaybe<Scalars['String']['input']>;
  rfid_card_number: Scalars['String']['input'];
  rfid_status: Scalars['String']['input'];
  user_id?: InputMaybe<Scalars['String']['input']>;
};

export type InsertRfidRecordResponse = {
  __typename?: 'InsertRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  addRFID?: Maybe<AddRfidResponse>;
  blockRFID?: Maybe<BlockRfidResponse>;
  deleteRfidData?: Maybe<DeleteRfidRecordResponse>;
  insertRfidData?: Maybe<InsertRfidRecordResponse>;
  replaceRFID?: Maybe<RfidResponse>;
  requestRFID?: Maybe<PaymentRfidResponse>;
  unblockRFID?: Maybe<UnblockRfidResponse>;
  updateRFIDInternal?: Maybe<UpdateRfidInternalResponse>;
  updateRfidData?: Maybe<UpdateRfidRecordResponse>;
};

export type MutationAddRfidArgs = {
  payload?: InputMaybe<AddRfid>;
};

export type MutationBlockRfidArgs = {
  payload?: InputMaybe<BlockRfid>;
};

export type MutationDeleteRfidDataArgs = {
  payload?: InputMaybe<DeleteRfidParamsObject>;
};

export type MutationInsertRfidDataArgs = {
  payload?: InputMaybe<InsertItem>;
};

export type MutationReplaceRfidArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type MutationRequestRfidArgs = {
  payload?: InputMaybe<RequestRfidRequest>;
};

export type MutationUnblockRfidArgs = {
  payload?: InputMaybe<UnblockRfid>;
};

export type MutationUpdateRfidInternalArgs = {
  payload?: InputMaybe<UpdateRfidInternalPayload>;
};

export type MutationUpdateRfidDataArgs = {
  payload?: InputMaybe<UpdateRfidParamsObject>;
};

/** Deprecated - PaymentAddressDetails */
export type PaymentAddressDetails = {
  address_city?: InputMaybe<Scalars['String']['input']>;
  address_country?: InputMaybe<Scalars['String']['input']>;
  address_line?: InputMaybe<Scalars['String']['input']>;
  address_postcode?: InputMaybe<Scalars['String']['input']>;
};

/** Updated - used to be PaymentAddressDetails */
export type PaymentAddressDetailsUpdated = {
  addressCity?: InputMaybe<Scalars['String']['input']>;
  addressCountry?: InputMaybe<Scalars['String']['input']>;
  addressLine?: InputMaybe<Scalars['String']['input']>;
  addressPostcode?: InputMaybe<Scalars['String']['input']>;
};

export type PaymentRfidResponse = {
  __typename?: 'PaymentRFIDResponse';
  data?: Maybe<PaymentRfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type PaymentRfidResponseData = {
  __typename?: 'PaymentRFIDResponseData';
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceID?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  fetchRfidDataByCardNumber?: Maybe<ExistingRfidRecordResponse>;
  fetchRfidDataByStatus?: Maybe<ExistingRfidRecordResponse>;
  getLatestRequestIdentifier?: Maybe<GetLatestRequestIdentifierResponse>;
  /** @deprecated Deprecated - used in UK app */
  requestRFID?: Maybe<DeprecatedPaymentRfidResponse>;
};

export type QueryFetchRfidDataByCardNumberArgs = {
  payload?: InputMaybe<GetRfidParamsObjectCard>;
};

export type QueryFetchRfidDataByStatusArgs = {
  payload?: InputMaybe<GetRfidParamsObjectStatus>;
};

export type QueryRequestRfidArgs = {
  addressDetails?: InputMaybe<PaymentAddressDetails>;
  cardPreference?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type RfidResponse = {
  __typename?: 'RFIDResponse';
  data?: Maybe<RfidResponseData>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type RfidResponseData = {
  __typename?: 'RFIDResponseData';
  eventDetails?: Maybe<Scalars['String']['output']>;
  eventTime?: Maybe<Scalars['String']['output']>;
  salesforceID?: Maybe<Scalars['String']['output']>;
};

export type RequestRfidRequest = {
  address: PaymentAddressDetailsUpdated;
  cardPreference?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type RevenuePlanInput = {
  provider?: InputMaybe<Scalars['String']['input']>;
  revenuePlanName?: InputMaybe<Scalars['String']['input']>;
};

export type UnblockRfid = {
  cardNumber: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  country: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type UnblockRfidResponse = {
  __typename?: 'UnblockRfidResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateRfidInternalPayload = {
  country?: InputMaybe<Scalars['String']['input']>;
  revenuePlan?: InputMaybe<Array<InputMaybe<RevenuePlanInput>>>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateRfidInternalResponse = {
  __typename?: 'UpdateRFIDInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateRfidRecordResponse = {
  __typename?: 'UpdateRFIDRecordResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateRfidParamsObject = {
  cardNumber: Scalars['String']['input'];
  requestIdentifier?: InputMaybe<Scalars['String']['input']>;
  status: Scalars['String']['input'];
};

export type BlockRfidMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  country: Scalars['String']['input'];
  reasonForBlocking: Scalars['String']['input'];
  cardUid: Scalars['String']['input'];
  cardNumber: Scalars['String']['input'];
}>;

export type BlockRfidMutation = {
  __typename?: 'Mutation';
  blockRFID?: {
    __typename?: 'BlockRfidResponse';
    status?: number | null;
    message?: string | null;
  } | null;
};

export const BlockRfidDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: { kind: 'Name', value: 'blockRFID' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'country' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'reasonForBlocking' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'cardUid' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'cardNumber' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'blockRFID' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'payload' },
                value: {
                  kind: 'ObjectValue',
                  fields: [
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'userId' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'userId' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'country' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'country' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'reasonForBlocking' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'reasonForBlocking' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'cardUid' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'cardUid' },
                      },
                    },
                    {
                      kind: 'ObjectField',
                      name: { kind: 'Name', value: 'cardNumber' },
                      value: {
                        kind: 'Variable',
                        name: { kind: 'Name', value: 'cardNumber' },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                { kind: 'Field', name: { kind: 'Name', value: 'status' } },
                { kind: 'Field', name: { kind: 'Name', value: 'message' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<BlockRfidMutation, BlockRfidMutationVariables>;
