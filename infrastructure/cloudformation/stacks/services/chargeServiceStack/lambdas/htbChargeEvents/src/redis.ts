import Redis, { RedisOptions } from 'ioredis';
import { EmspChargeEvent, EmspEventType } from './mappers';
import { env } from './env';

const options: RedisOptions = {
  host: env.ELASTICACHE_HOST,
  port: parseInt(env.ELASTICACHE_PORT),
};

if (env.NODE_ENV !== 'local') {
  options.tls = {};
}

let instance: Redis.Redis | null;

const client = () => {
  if (!instance) {
    instance = new Redis(options);

    instance.on('error', (e) => {
      console.error(`Redis error: ${e.message}`, e);
    });
  }

  return instance;
};

export const quit = () => {
  const result = client().quit();
  instance = null;
  return result;
};

export const setCacheItem = async (chargeEvent: EmspChargeEvent) => {
  if (!chargeEvent.userId) {
    console.warn('Missing user id on charge event', chargeEvent);
    return;
  }

  const eventString = JSON.stringify(chargeEvent);

  // Set the expiracy to 12 hours if start charge is confirmed
  if (
    chargeEvent.eventDetails === EmspEventType.START_CHARGE_RESPONSE &&
    chargeEvent.chargePayload.status === 'Success'
  ) {
    // if the charge has been started, set the cache's expiry time to 90 minutes
    await client().set(chargeEvent.userId, eventString, 'EX', 5400);
    console.debug(
      `Setting start charge value for userId ${chargeEvent.userId}`,
    );
  }

  await client().set(chargeEvent.userId, eventString, 'EX', 360);
  console.debug(`Setting stop charge value for userId ${chargeEvent.userId}`);
};
