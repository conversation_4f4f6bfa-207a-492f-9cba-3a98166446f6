import { handler } from './index';
import { getTodayStart, getYesterdayStart } from './utils/date/date';
import { validateAndNormalizeDates } from './utils/date/validateDates';
import { getAllSubsTransactions } from './services/getAllSubsTransactions/getAllSubsTransactions';
import { logger } from './utils/logger/logger';
import { uploadCSV } from './utils/csv/csv';
import { getReceiptDetails } from './services/getReceiptDetail/getReceiptDetail';
import { mergeSubscriptionRecord } from './utils/transformations/dataMerge';
import { PaymentStatus } from './models/SubsTransaction';
import {
  capturedResultNewer,
  capturedResult,
} from './mocks/getAllSubsTransactions.mock';

import {
  object1Merged,
  object2Merged,
  object3Merged,
} from './mocks/mergedTrasactions.mock';

jest.mock('./utils/date/date');
jest.mock('./utils/date/validateDates');
jest.mock('./services/getAllSubsTransactions/getAllSubsTransactions');
jest.mock('./services/getReceiptDetail/getReceiptDetail');
jest.mock('./utils/transformations/dataMerge');
jest.mock('./utils/csv/csv');
jest.mock('./utils/logger/logger');
jest.mock('./env/env');

const mockedGetYesterdayStart = getYesterdayStart as jest.MockedFunction<
  typeof getYesterdayStart
>;
const mockedGetTodayStart = getTodayStart as jest.MockedFunction<
  typeof getTodayStart
>;
const mockedValidateAndNormalizeDates =
  validateAndNormalizeDates as jest.MockedFunction<
    typeof validateAndNormalizeDates
  >;
const mockedGetAllSubsTransactions =
  getAllSubsTransactions as jest.MockedFunction<typeof getAllSubsTransactions>;
const mockedGetReceiptDetails = getReceiptDetails as jest.MockedFunction<
  typeof getReceiptDetails
>;
const mockedMergeSubscriptionRecord =
  mergeSubscriptionRecord as jest.MockedFunction<
    typeof mergeSubscriptionRecord
  >;
const mockedUploadCSV = uploadCSV as jest.MockedFunction<typeof uploadCSV>;
const mockedLoggerError = logger.error as jest.MockedFunction<
  typeof logger.error
>;
const mockedLoggerInfo = logger.info as jest.MockedFunction<typeof logger.info>;

describe('handler', () => {
  const mockStartDate = new Date('2023-01-01T00:00:00.000Z');
  const mockEndDate = new Date('2023-01-02T00:00:00.000Z'); // Changed to next day at 00:00:00
  const mockAllSubsTransactions = [capturedResultNewer, capturedResult];
  const mockReceiptDetails = {
    getReceiptDetail: {
      receiptDetail: [
        { receipt_id: 'r1', transaction_id: '1', acquirer: 'Elavon' },
      ],
      status: 'success',
      success: true,
      message: 'success',
    },
  };
  const mockMergedTransactions = [object1Merged, object2Merged, object3Merged];
  const mockPutResponse = 'Success';

  beforeEach(() => {
    jest.clearAllMocks();

    mockedGetYesterdayStart.mockReturnValue(mockStartDate);
    mockedGetTodayStart.mockReturnValue(mockEndDate);
    mockedGetAllSubsTransactions.mockResolvedValue(mockAllSubsTransactions);
    mockedGetReceiptDetails.mockResolvedValue(mockReceiptDetails);
    mockedMergeSubscriptionRecord.mockReturnValue(mockMergedTransactions);
    mockedUploadCSV.mockResolvedValue(mockPutResponse as any);
  });

  it('should process the event successfully with default dates', async () => {
    const result = await handler({}, null as any, null as any);

    expect(mockedGetYesterdayStart).toHaveBeenCalled();
    expect(mockedGetTodayStart).toHaveBeenCalled();
    expect(mockedGetAllSubsTransactions).toHaveBeenCalledWith(
      mockStartDate.toISOString(),
      mockEndDate.toISOString(),
      PaymentStatus.CAPTURED,
    );
    expect(mockedMergeSubscriptionRecord).toHaveBeenCalledWith(
      mockAllSubsTransactions,
      mockReceiptDetails.getReceiptDetail.receiptDetail,
    );
    expect(mockedUploadCSV).toHaveBeenCalledWith(
      mockMergedTransactions,
      mockStartDate,
      mockEndDate,
    );
    expect(mockedLoggerInfo).toHaveBeenCalledWith(
      `putResponse: ${mockPutResponse}`,
    );
    expect(result).toEqual({
      statusCode: 200,
      body: JSON.stringify('Success'),
    });
  });

  it('should process the event successfully with custom dates from event', async () => {
    const customStartDate = '2023-02-01T00:00:00.000Z';
    const customEndDate = '2023-02-02T00:00:00.000Z';

    mockedValidateAndNormalizeDates.mockReturnValue({
      startDate: customStartDate,
      endDate: customEndDate,
    });

    const result = await handler(
      { startDate: customStartDate, endDate: customEndDate },
      null as any,
      null as any,
    );

    expect(mockedValidateAndNormalizeDates).toHaveBeenCalledWith(
      customStartDate,
      customEndDate,
    );
    expect(mockedGetAllSubsTransactions).toHaveBeenCalledWith(
      customStartDate,
      customEndDate,
      PaymentStatus.CAPTURED,
    );
    expect(mockedUploadCSV).toHaveBeenCalledWith(
      mockMergedTransactions,
      new Date(customStartDate),
      new Date(customEndDate),
    );
    expect(result).toEqual({
      statusCode: 200,
      body: JSON.stringify('Success'),
    });
  });

  it('should normalize dates when custom dates have time components', async () => {
    const customStartDate = '2023-02-01T15:30:00.000Z';
    const customEndDate = '2023-02-01T23:59:59.999Z';
    const normalizedStartDate = 'Thu, 01 Feb 2023 00:00:00 GMT';
    const normalizedEndDate = 'Fri, 02 Feb 2023 00:00:00 GMT';

    mockedValidateAndNormalizeDates.mockReturnValue({
      startDate: normalizedStartDate,
      endDate: normalizedEndDate,
    });

    const result = await handler(
      { startDate: customStartDate, endDate: customEndDate },
      null as any,
      null as any,
    );

    expect(mockedValidateAndNormalizeDates).toHaveBeenCalledWith(
      customStartDate,
      customEndDate,
    );

    expect(mockedLoggerInfo).toHaveBeenCalledWith(
      `Processing sub transactions from ${new Date(
        normalizedStartDate,
      ).toISOString()} to ${new Date(normalizedEndDate).toISOString()}`,
    );

    expect(mockedGetAllSubsTransactions).toHaveBeenCalledWith(
      new Date(normalizedStartDate).toISOString(),
      new Date(normalizedEndDate).toISOString(),
      PaymentStatus.CAPTURED,
    );
  });

  it('should log processing date range', async () => {
    const result = await handler({}, null as any, null as any);

    expect(mockedLoggerInfo).toHaveBeenCalledWith(
      `Processing sub transactions from ${mockStartDate.toISOString()} to ${mockEndDate.toISOString()}`,
    );
  });

  it('should log and return success if no transactions found', async () => {
    mockedMergeSubscriptionRecord.mockReturnValue([]);

    const result = await handler({}, null as any, null as any);

    expect(mockedUploadCSV).not.toHaveBeenCalled();
    expect(mockedLoggerInfo).toHaveBeenCalledWith('No transactions found');
    expect(result).toEqual({
      statusCode: 200,
      body: JSON.stringify('No transactions found'),
    });
  });

  it('should handle errors gracefully', async () => {
    const mockError = new Error('Test error');
    mockedGetAllSubsTransactions.mockRejectedValueOnce(mockError);

    await expect(handler({}, null as any, null as any)).rejects.toThrow(
      'Test error',
    );
    expect(mockedLoggerError).toHaveBeenCalledWith(
      'dailySubsFinanceData failure',
    );
    expect(mockedLoggerError).toHaveBeenCalledWith(mockError);
  });

  it('should handle date validation errors', async () => {
    const customStartDate = '2023-02-02T00:00:00.000Z';
    const customEndDate = '2023-02-01T00:00:00.000Z'; // End before start

    mockedValidateAndNormalizeDates.mockImplementation(() => {
      throw new Error('Invalid date range');
    });

    await expect(
      handler(
        { startDate: customStartDate, endDate: customEndDate },
        null as any,
        null as any,
      ),
    ).rejects.toThrow('Invalid date range');

    expect(mockedValidateAndNormalizeDates).toHaveBeenCalledWith(
      customStartDate,
      customEndDate,
    );
  });

  it('should handle non-Error exceptions', async () => {
    const mockError = 'String error';
    mockedGetAllSubsTransactions.mockRejectedValueOnce(mockError);

    await expect(handler({}, null as any, null as any)).rejects.toThrow(
      'dailySubsFinanceData error for event {}',
    );
    expect(mockedLoggerError).toHaveBeenCalledWith(
      'dailySubsFinanceData failure',
    );
    expect(mockedLoggerError).toHaveBeenCalledWith(mockError);
  });
});
