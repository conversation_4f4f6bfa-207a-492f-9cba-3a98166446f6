import Utils from '../../shared/utils';
import links from '../../test-data/links/links.json';

describe('Verify content for the links:', () => {
  links.forEach(({ name, url, expected }) => {
    it('should get content for the link to ' + name, async () => {
      const headers = {
        Accept: 'text/html',
        'User-Agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
      };

      let response;
      async function makeRequest() {
        try {
          response = await Utils.sendHttpRequest(
            'GET',
            url,
            '',
            {},
            {},
            headers,
          );
        } catch (error) {
          console.error('Request failed:', error);
        }
      }
      await makeRequest();
      expect(response.status).toBe(200);
      expected.forEach((item) => {
        expect(response.text).toContain(item);
      });
    });
  });
});
