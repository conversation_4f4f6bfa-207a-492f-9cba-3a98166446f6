import { DeactivationCommandFactory } from '../src/commands/deactivation-factory';
import {
    DcsDeactivationCommand,
    HtbDeactivationCommand,
    RfidDeactivationCommand,
} from '../src/commands/deactivation-commands';
import { Tag } from '../src/types/types';

jest.mock('../src/env', () => ({
    env: require('./mocks/env.mock').env,
}));
jest.mock('../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

describe('DeactivationCommandFactory', () => {
    const userId = 'user-123';
    const country = 'NL';

    it('should create DcsDeactivationCommand for DCS tag type', () => {
        const dcsTag: Tag = {
            tagId: '12345',
            tagNotes: 'virtual-DCS',
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        const command = DeactivationCommandFactory.createCommand(
            dcsTag,
            userId,
            country,
        );

        expect(command).toBeInstanceOf(DcsDeactivationCommand);
    });

    it('should create HtbDeactivationCommand for HTB tag type', () => {
        const htbTag: Tag = {
            tagId: '12345',
            tagNotes: 'virtual-HTB',
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        const command = DeactivationCommandFactory.createCommand(
            htbTag,
            userId,
            country,
        );

        expect(command).toBeInstanceOf(HtbDeactivationCommand);
    });

    it('should create RfidDeactivationCommand for RFID tag type', () => {
        const rfidTag: Tag = {
            tagId: '12345',
            tagNotes: 'physical-RFID',
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        const command = DeactivationCommandFactory.createCommand(
            rfidTag,
            userId,
            country,
        );

        expect(command).toBeInstanceOf(RfidDeactivationCommand);
    });

    it('should be case-insensitive when determining tag type', () => {
        const lowercaseTag: Tag = {
            tagId: '12345',
            tagNotes: 'virtual-dcs', // lowercase
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        const command = DeactivationCommandFactory.createCommand(
            lowercaseTag,
            userId,
            country,
        );

        expect(command).toBeInstanceOf(DcsDeactivationCommand);
    });

    it('should throw an error for unsupported tag type', () => {
        const unknownTag: Tag = {
            tagId: '12345',
            tagNotes: 'unknown-tag-type',
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        expect(() => {
            DeactivationCommandFactory.createCommand(
                unknownTag,
                userId,
                country,
            );
        }).toThrow('Unsupported tag type');
    });
});
