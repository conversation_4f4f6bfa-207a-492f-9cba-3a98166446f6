import { Tag } from '../types/types';
import {
    BlockRFIDInput,
    DeactivateHTBCardInput,
    QueryDefaultResponse,
    ReasonForBlocking,
    TagStatus,
    TerminateDCSContractInput,
} from '../utils/constants';
import { dcsTerminateContract } from '../utils/dcs';
import { deactivateHTBCard } from '../utils/htb';
import { logger } from '../utils/logger';
import { blockRFID } from '../services/rfid';
import { updateTagInternal } from '../services/user';

export interface DeactivationCommand {
    execute(): Promise<QueryDefaultResponse>;
}

export abstract class BaseDeactivationCommand implements DeactivationCommand {
    protected tag: Tag;
    protected userId: string | undefined;
    protected country: string;

    constructor(tag: Tag, userId: string | undefined, country: string) {
        this.tag = tag;
        this.userId = userId;
        this.country = country;
    }

    async execute(): Promise<QueryDefaultResponse> {
        try {
            if (!this.validateTag()) {
                return {
                    status: 400,
                    message: `Tag cannot be updated because it's missing key data. tagCardNumber, tagId, tagNotes and tagStatus is required`,
                };
            }

            const response = await this.deactivateSpecificTagType();

            if (response.status === 200 && this.updateTagStatus) {
                await this.updateTagStatus();
            }

            return response;
        } catch (error) {
            logger.error(
                `There has been an error while deactivating the ${this.tag.tagNotes} tag: ${error}`,
            );
            return {
                status: 500,
                message: `Error deactivating tag: ${error}`,
            };
        }
    }

    protected validateTag(): boolean {
        return !!(
            this.tag.tagNotes &&
            this.tag.tagId &&
            this.tag.tagCardNumber &&
            this.tag.tagStatus
        );
    }

    protected abstract deactivateSpecificTagType(): Promise<QueryDefaultResponse>;

    protected updateTagStatus?(): Promise<void>;
}

export class DcsDeactivationCommand extends BaseDeactivationCommand {
    protected async deactivateSpecificTagType(): Promise<QueryDefaultResponse> {
        const params: TerminateDCSContractInput = {
            dcsContractId: this.tag.tagId,
            country: this.country,
        };

        return await dcsTerminateContract(params);
    }

    protected async updateTagStatus(): Promise<void> {
        await updateTagInternal({
            salesforceId: this.userId || 'NO-USER-ID',
            country: this.country,
            tagCardNumber: this.tag.tagCardNumber,
            tagStatus: TagStatus.TERMINATED,
            tagBarredDatetime: new Date().toISOString(),
        });
    }
}

export class HtbDeactivationCommand extends BaseDeactivationCommand {
    protected async deactivateSpecificTagType(): Promise<QueryDefaultResponse> {
        const params: DeactivateHTBCardInput = {
            serialNumber: this.tag.tagId,
            cardNumber: this.tag.tagId,
            country: this.country,
        };

        return await deactivateHTBCard(params);
    }

    protected async updateTagStatus(): Promise<void> {
        await updateTagInternal({
            salesforceId: this.userId || 'DUMMY-USER-ID',
            country: this.country,
            tagCardNumber: this.tag.tagCardNumber,
            tagStatus: 'TERMINATED',
            tagBarredDatetime: new Date().toISOString(),
        });
    }
}

export class RfidDeactivationCommand extends BaseDeactivationCommand {
    async execute(): Promise<QueryDefaultResponse> {
        try {
            if (!this.validateTag()) {
                return {
                    status: 400,
                    message: `Tag cannot be updated because it's missing key data. tagCardNumber, tagId, tagNotes and tagStatus is required`,
                };
            }

            if (!this.userId) {
                const message = `Cannot block RFID as userId is not defined, tagSerialNumber: ${this.tag.tagId}, tagCardNumber:${this.tag.tagCardNumber}`;
                logger.info(message);
                return {
                    status: 400,
                    message,
                };
            }

            return await this.deactivateSpecificTagType();
        } catch (error) {
            logger.error(
                `There has been an error while deactivating the ${this.tag.tagNotes} tag: ${error}`,
            );
            return {
                status: 500,
                message: `Error deactivating tag: ${error}`,
            };
        }
    }

    protected async deactivateSpecificTagType(): Promise<QueryDefaultResponse> {
        const params: BlockRFIDInput = {
            userId: this.userId!,
            country: this.country,
            reasonForBlocking: ReasonForBlocking.RequestToCancel,
            cardUid: this.tag.tagId,
            cardNumber: this.tag.tagCardNumber,
        };

        return await blockRFID(params);
    }
}
