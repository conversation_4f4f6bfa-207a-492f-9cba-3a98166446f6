{"name": "get-data-dcs", "version": "1.0.0", "private": true, "main": "src/index.ts", "scripts": {"prebuild": "npm run clean", "build": "npm i && npm i xml-stream && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "postinstall": "rm -rf ./node_modules/hpagent/test", "start": "ts-node src/run-local.ts", "test": "jest", "test:dev": "jest --watch", "types:check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.449.0", "@elastic/elasticsearch": "7.13.0", "axios": "^1.10.0", "dotenv": "^16.0.1", "lodash": "^4.17.21", "request": "^2.88.2", "unzipper": "^0.10.11"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.100", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.181", "@types/request": "^2.48.8", "@types/unzipper": "^0.10.5", "axios-mock-adapter": "^1.20.0", "babel-jest": "^29.5.0", "jest": "^29.5.0", "lambda-local": "^2.0.2", "ts-node": "^10.7.0", "typescript": "^4.6.2"}, "peerDependencies": {"xml-stream": "^0.4.5"}}