import {
  DateType,
  PaymentStatus,
  PROVIDER,
  TransactionType,
} from './common/enums';
import {
  addSAPFields,
  computeDCSEntries,
  validateAndNormalizeDates,
} from './common/helperFunctions';
import { DCSEntry, GenericObject } from './common/interfaces';
import { getTransactions } from './services/bppay';
import { getBPCMDCSHTBEntries } from './services/history';
import { getReceiptDetail } from './services/receipt';
import { putCSVFile } from './utils/csvGenerator';
import {
  getTodaysEndDate,
  getYesterdaysStartDate,
} from './utils/dateUtils/dateUtils';

const triggerDailyFinanceData = async (event: any) => {
  // compute dates for get transactions api call
  // get the transactions from the previous day
  let startDate = event?.startDate || getYesterdaysStartDate();
  let endDate = event?.endDate || getTodaysEndDate();

  // Validate and normalize dates if provided by input event
  // This is needed to avoid the common testing pitfall of providing dates with time 23:59:59 instead of 00:00:00
  if (event?.startDate && event?.endDate) {
    const normalized = validateAndNormalizeDates(
      event.startDate,
      event.endDate,
    );
    startDate = normalized.startDate;
    endDate = normalized.endDate;
  }

  console.log(`Getting transactions from ${startDate} to ${endDate}`);

  const dateType = process.env.DATE_TYPE || DateType.ORDER_STARTED;

  const [registeredTransactions, rfidTransactions] = await Promise.all([
    getTransactions(
      startDate,
      endDate,
      [
        PaymentStatus.CAPTURED,
        PaymentStatus.CAPTURED.toLowerCase(),
        PaymentStatus.REFUNDED,
        PaymentStatus.REFUNDED.toLowerCase(),
      ],
      TransactionType.REGISTERED,
      dateType,
    ),
    getTransactions(
      startDate,
      endDate,
      [
        PaymentStatus.CAPTURED,
        PaymentStatus.CAPTURED.toLowerCase(),
        PaymentStatus.REFUNDED,
        PaymentStatus.REFUNDED.toLowerCase(),
      ],
      TransactionType.RFID,
      dateType,
    ),
  ]);

  const transactions = [...registeredTransactions, ...rfidTransactions];

  console.log(
    `Captured ${registeredTransactions?.length} REGISTERED and ${rfidTransactions?.length} RFID transactions (${transactions.length} total)...`,
  );

  // map array of charge session ids for captured transactions

  const chargeSessionIDs = transactions
    .filter(
      (t: any) =>
        t.final_payment_status.toLowerCase() ===
        PaymentStatus.CAPTURED.toLowerCase(),
    )
    .map((transaction: any) => transaction.charge_session_id)
    .filter(
      (id: string | null) => id !== null && id !== '' && id !== undefined,
    );

  const referenceChargeSessionIDs = transactions
    .filter(
      (t: any) =>
        t.final_payment_status.toLowerCase() ===
        PaymentStatus.REFUNDED.toLowerCase(),
    )
    .map((transaction: any) => transaction.reference_charge_session_id)
    .filter(
      (id: string | null) => id !== null && id !== '' && id !== undefined,
    );

  const allChargeSessionIDs = [
    ...chargeSessionIDs,
    ...referenceChargeSessionIDs,
  ];
  console.log(
    `Total chargeSessionIDs to query: ${allChargeSessionIDs.length}`,
    allChargeSessionIDs,
  ); // Log all IDs being sent to getBPCMDCSHTBEntries

  let { hasToBeEntries, dcsRecords, bpcmEntries } = await getBPCMDCSHTBEntries(
    allChargeSessionIDs,
  );
  console.log(`Found ${hasToBeEntries?.length} HasToBe Entries`);
  console.log(`Found ${dcsRecords?.length} DCS Entries`);
  console.log(`Found ${bpcmEntries?.length} BPCM Entries`);

  console.log(`HasToBe Records...`, JSON.stringify(hasToBeEntries, null, 3));
  console.log(`DCS Records...`, JSON.stringify(dcsRecords, null, 3));
  console.log(`BPCM Records...`, JSON.stringify(bpcmEntries, null, 3));
  // map session ids and charge session ids to cdr file names
  const mappedFileNames: GenericObject = {};

  dcsRecords.forEach((entry: DCSEntry) => {
    Object.keys(mappedFileNames)?.includes(entry.cdr_filename)
      ? mappedFileNames[entry.cdr_filename].push(entry)
      : (mappedFileNames[entry.cdr_filename] = [entry]);
  });

  console.log(
    'DCS: File to session ID mapping...',
    JSON.stringify(mappedFileNames, null, 3),
  );

  //compute dcs entries from S3 files
  const parsedDCSEntries: Array<any> = await computeDCSEntries(mappedFileNames);

  let dcsEntries: Array<GenericObject> = parsedDCSEntries.flat();

  // fetch receipt details for chargeSessionIds
  const receiptDetails = await getReceiptDetail(
    chargeSessionIDs.concat(referenceChargeSessionIDs),
  );

  console.log('Fetched receipt details...', JSON.stringify(receiptDetails));

  // combine transaction and receipt data into one array
  const transactionReceiptDetails = receiptDetails.receiptDetail
    .filter((el) => el)
    .map((details) => ({
      ...transactions.find(
        (t: { charge_session_id: any; reference_charge_session_id: any }) =>
          t.charge_session_id == details.charge_session_id ||
          t.reference_charge_session_id === details.charge_session_id,
      ),
      receiptDetails: details,
    }));

  // add SAP specific fields to htb entries
  dcsEntries = await addSAPFields(dcsEntries, transactionReceiptDetails);
  hasToBeEntries = await addSAPFields(
    hasToBeEntries,
    transactionReceiptDetails,
  );
  bpcmEntries = await addSAPFields(bpcmEntries, transactionReceiptDetails);

  console.log(`Enriched DCS Records...`, JSON.stringify(dcsEntries));
  console.log(`Enriched HasToBe Records...`, JSON.stringify(hasToBeEntries));
  console.log(`Enriched BPCM Records...`, JSON.stringify(bpcmEntries));

  await putCSVFile(PROVIDER.DCS, dcsEntries, startDate, endDate);
  await putCSVFile(PROVIDER.HASTOBE, hasToBeEntries, startDate, endDate);
  await putCSVFile(PROVIDER.BPCM, bpcmEntries, startDate, endDate);
  return { message: 'Success' };
};

export const handler = async (event: any) => {
  try {
    return await triggerDailyFinanceData(event);
  } catch (error: any) {
    console.log('Error on triggering Daily Finance Data Lambda ', error);
    return {
      status: 500,
      message: error.message,
    };
  }
};
