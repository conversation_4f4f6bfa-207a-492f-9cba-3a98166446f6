import { Readable } from 'node:stream';
import { put } from '../../clients/s3';
import { PROVIDER } from '../../common/enums';
import { writeToS3 } from './writeToS3';

jest.mock('../../clients/s3');

jest.spyOn(console, 'info').mockImplementation(jest.fn());

describe('writeToS3', () => {
  const buffer = Buffer.from('test-data');
  const gzData = { stream: new Readable(), length: 0 };
  const startDate = '2019-12-03'; // Data date
  const endDate = '2019-12-04'; // Runtime date (path date)

  afterEach(() => {
    (put as jest.Mock).mockReset();
  });

  describe('Providers', () => {
    it('should write file to S3 successfully for BPCM', async () => {
      (put as jest.Mock).mockImplementation(jest.fn());

      await writeToS3(PROVIDER.BPCM, buffer, gzData, startDate, endDate);

      expect(console.info).toHaveBeenCalledWith(
        'File written successfully to S3 for: BPCM',
      );
      expect(put).toHaveBeenNthCalledWith(
        1,
        'ws-00f0-dcs-outbound-roaming',
        'Sales_invoice/Invoices/BPCM/upload_date=2019-12-04/EMSP_BPCM_INV_20191203.csv',
        buffer,
      );
      expect(put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/daily_sales_invoices/BPCM/2019/12/04/EMSP_BPCM_INV_20191203.csv.gz',
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    });

    it('should write file to S3 successfully for DCS', async () => {
      (put as jest.Mock).mockImplementation(jest.fn());

      await writeToS3(PROVIDER.DCS, buffer, gzData, startDate, endDate);

      expect(console.info).toHaveBeenCalledWith(
        'File written successfully to S3 for: DCS',
      );
      expect(put).toHaveBeenNthCalledWith(
        1,
        'ws-00f0-dcs-outbound-roaming',
        'Sales_invoice/Invoices/DCS/upload_date=2019-12-04/EMSP_DCS_INV_20191203.csv',
        buffer,
      );
      expect(put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/daily_sales_invoices/DCS/2019/12/04/EMSP_DCS_INV_20191203.csv.gz',
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    });

    it('should write file to S3 successfully for HTB', async () => {
      (put as jest.Mock).mockImplementation(jest.fn());

      await writeToS3(PROVIDER.HASTOBE, buffer, gzData, startDate, endDate);

      expect(console.info).toHaveBeenCalledWith(
        'File written successfully to S3 for: HASTOBE',
      );
      expect(put).toHaveBeenNthCalledWith(
        1,
        'ws-00f0-dcs-outbound-roaming',
        'Sales_invoice/Invoices/HTB/upload_date=2019-12-04/EMSP_HTB_INV_20191203.csv',
        buffer,
      );
      expect(put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/daily_sales_invoices/HTB/2019/12/04/EMSP_HTB_INV_20191203.csv.gz',
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    });
  });

  it('should throw error when writing file to S3 fails', async () => {
    const errorMessage = 'Test error message';
    const error = new Error(errorMessage);

    (put as jest.Mock).mockRejectedValue(error);

    await expect(
      writeToS3(PROVIDER.BPCM, buffer, gzData, startDate, endDate),
    ).rejects.toThrow(
      `Error writing file to S3 for BPCM with error: Error: ${errorMessage}`,
    );

    expect(put).toHaveBeenNthCalledWith(
      1,
      'ws-00f0-dcs-outbound-roaming',
      'Sales_invoice/Invoices/BPCM/upload_date=2019-12-04/EMSP_BPCM_INV_20191203.csv',
      buffer,
    );
  });

  describe('Date handling edge cases', () => {
    it('should handle daily run pattern correctly', async () => {
      (put as jest.Mock).mockImplementation(jest.fn());

      // Processing July 9th data on July 10th
      const dailyStartDate = '2025-07-09';
      const dailyEndDate = '2025-07-10';

      await writeToS3(
        PROVIDER.DCS,
        buffer,
        gzData,
        dailyStartDate,
        dailyEndDate,
      );

      expect(put).toHaveBeenNthCalledWith(
        1,
        'ws-00f0-dcs-outbound-roaming',
        'Sales_invoice/Invoices/DCS/upload_date=2025-07-10/EMSP_DCS_INV_20250709.csv',
        buffer,
      );
      expect(put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/daily_sales_invoices/DCS/2025/07/10/EMSP_DCS_INV_20250709.csv.gz',
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    });

    it('should handle re-generation pattern correctly', async () => {
      (put as jest.Mock).mockImplementation(jest.fn());

      // Re-Generating March 15th data
      const missingFileStartDate = '2025-03-15';
      const missingFileEndDate = '2025-03-16';

      await writeToS3(
        PROVIDER.HASTOBE,
        buffer,
        gzData,
        missingFileStartDate,
        missingFileEndDate,
      );

      expect(put).toHaveBeenNthCalledWith(
        1,
        'ws-00f0-dcs-outbound-roaming',
        'Sales_invoice/Invoices/HTB/upload_date=2025-03-16/EMSP_HTB_INV_20250315.csv',
        buffer,
      );
      expect(put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/daily_sales_invoices/HTB/2025/03/16/EMSP_HTB_INV_20250315.csv.gz',
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    });
  });
});
