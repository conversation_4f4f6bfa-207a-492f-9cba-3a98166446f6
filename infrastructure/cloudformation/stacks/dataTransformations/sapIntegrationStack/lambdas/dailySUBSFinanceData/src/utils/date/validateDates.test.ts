import { validateAndNormalizeDates } from './validateDates';

describe('validateAndNormalizeDates', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'log').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Start date normalization', () => {
    it('should normalize start date to 00:00:00 when time is provided', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T15:30:45Z',
        '2025-07-10T00:00:00Z',
      );

      const expectedStart = new Date('2025-07-09T00:00:00.000Z');
      expect(new Date(result.startDate).toISOString()).toBe(
        expectedStart.toISOString(),
      );
    });

    it('should keep start date at 00:00:00 when already normalized', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-10T00:00:00Z',
      );

      const expectedStart = new Date('2025-07-09T00:00:00.000Z');
      expect(new Date(result.startDate).toISOString()).toBe(
        expectedStart.toISOString(),
      );
    });
  });

  describe('End date normalization', () => {
    it('should adjust end date with 23:59:59 to next day at 00:00:00', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-09T23:59:59Z',
      );

      const expectedEnd = new Date('2025-07-10T00:00:00.000Z');
      expect(new Date(result.endDate).toISOString()).toBe(
        expectedEnd.toISOString(),
      );
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Invalid end date'),
      );
    });

    it('should adjust end date with any non-zero time to next day', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-09T12:30:00Z',
      );

      const expectedEnd = new Date('2025-07-10T00:00:00.000Z');
      expect(new Date(result.endDate).toISOString()).toBe(
        expectedEnd.toISOString(),
      );
      expect(console.warn).toHaveBeenCalled();
    });

    it('should keep end date unchanged when already at 00:00:00', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-10T00:00:00Z',
      );

      const expectedEnd = new Date('2025-07-10T00:00:00.000Z');
      expect(new Date(result.endDate).toISOString()).toBe(
        expectedEnd.toISOString(),
      );
      expect(console.warn).not.toHaveBeenCalled();
    });

    it('should handle milliseconds in the time component', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-09T23:59:59.999Z',
      );

      const expectedEnd = new Date('2025-07-10T00:00:00.000Z');
      expect(new Date(result.endDate).toISOString()).toBe(
        expectedEnd.toISOString(),
      );
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('Date validation', () => {
    it('should throw error when end date is before start date', () => {
      expect(() => {
        validateAndNormalizeDates(
          '2025-07-10T00:00:00Z',
          '2025-07-09T00:00:00Z',
        );
      }).toThrow('Invalid date range: endDate');
    });

    it('should throw error when end date equals start date after normalization', () => {
      expect(() => {
        validateAndNormalizeDates(
          '2025-07-09T00:00:00Z',
          '2025-07-09T00:00:00Z',
        );
      }).toThrow('Invalid date range: endDate');
    });

    it('should accept valid date ranges', () => {
      expect(() => {
        validateAndNormalizeDates(
          '2025-07-09T00:00:00Z',
          '2025-07-10T00:00:00Z',
        );
      }).not.toThrow();
    });
  });

  describe('Large date range warnings', () => {
    it('should not log for 1 day range', () => {
      validateAndNormalizeDates('2025-07-09T00:00:00Z', '2025-07-10T00:00:00Z');

      expect(console.log).not.toHaveBeenCalled();
    });

    it('should log warning for 2 day range', () => {
      validateAndNormalizeDates('2025-07-09T00:00:00Z', '2025-07-11T00:00:00Z');

      expect(console.log).toHaveBeenCalledWith(
        'Large date range detected: 2 days. ',
      );
    });

    it('should log warning for 7 day range', () => {
      validateAndNormalizeDates('2025-07-09T00:00:00Z', '2025-07-16T00:00:00Z');

      expect(console.log).toHaveBeenCalledWith(
        'Large date range detected: 7 days. ',
      );
    });
  });

  describe('Return format', () => {
    it('should return dates in UTC string format', () => {
      const result = validateAndNormalizeDates(
        '2025-07-09T00:00:00Z',
        '2025-07-10T00:00:00Z',
      );

      expect(result.startDate).toMatch(/GMT$/);
      expect(result.endDate).toMatch(/GMT$/);
      expect(result.startDate).toBe('Wed, 09 Jul 2025 00:00:00 GMT');
      expect(result.endDate).toBe('Thu, 10 Jul 2025 00:00:00 GMT');
    });
  });

  describe('Edge cases', () => {
    it('should handle daylight saving time transitions', () => {
      const result = validateAndNormalizeDates(
        '2025-03-09T00:00:00Z',
        '2025-03-10T00:00:00Z',
      );

      const expectedStart = new Date('2025-03-09T00:00:00.000Z');
      const expectedEnd = new Date('2025-03-10T00:00:00.000Z');

      expect(new Date(result.startDate).toISOString()).toBe(
        expectedStart.toISOString(),
      );
      expect(new Date(result.endDate).toISOString()).toBe(
        expectedEnd.toISOString(),
      );
    });

    it('should handle invalid date strings', () => {
      expect(() => {
        validateAndNormalizeDates('invalid-date', '2025-07-10T00:00:00Z');
      }).toThrow();
    });

    it('should handle dates in different formats', () => {
      const result = validateAndNormalizeDates('2025-07-09', '2025-07-10');

      expect(new Date(result.startDate).getUTCHours()).toBe(0);
      expect(new Date(result.endDate).getUTCHours()).toBe(0);
    });
  });
});
