jobs:
  - deployment: deploy_to_cluster
    pool:
      ${{ if eq(parameters.env, 'pr') }}:
        name: ContractTestsAgent
      ${{ if ne(parameters.env, 'pr') }}:
        name: 'Azure Pipelines'
        vmImage: 'ubuntu-latest'
    condition: and( ne( canceled(), True), or(eq(stageDependencies.detect_changes.detect_changes.outputs['detect_changes_${{ parameters.stageName }}.${{ parameters.stageName }}Update'], 'true'), ne( '${{ parameters.env }}', 'dev'), contains(variables['Build.Reason'], 'Manual')))
    displayName: Deploy to Cluster
    environment: Pulse-Apps-Backend-${{ parameters.env }}
    strategy:
      runOnce:
        deploy:
          steps:
            - template: ../../templates/AwsAuthenticationPs1.yaml
              parameters:
                IAMRole: '$(AwsLogicalAccountNameUpperCase)-role_AUTOMATION'
            - template: ../../templates/set_kubectl_version.yaml
            - checkout: self
              persistCredentials: true
            - template: ../steps/git-checkout-script.yaml
              parameters:
                tag: HEAD
            - task: qetza.replacetokens.replacetokens-task.replacetokens@6
              displayName: 'replace server manifest tokens'
              inputs:
                sources: '${{ parameters.deploymentFilePath }}'
                ifNoFilesFound: error
              env:
                FORCED_RESTART_VARIABLE: '$(Build.BuildId)'

            - ${{ if notIn(parameters.env, 'devops', 'pr', 'dev', 'test', 'performance', 'preprod')}}:
                - ${{ if in(parameters.name, 'charge-server', 'history-server', 'offer-server', 'subscription-server', 'rfid-server', 'pdf-server', 'prices-server', 'wallet-server', 'ocpi-server', 'invoices-server', 'favourites-server', 'voucher-server', 'user-server', 'payments-bppay-server', 'payments-stripe-server', 'payments-gocardless-server', 'map-server', 'anonymous-user-server', 'test-provider-server', 'gateway-private-server', 'gateway-public-server')}}:
                    - task: qetza.replacetokens.replacetokens-task.replacetokens@6
                      displayName: 'replace shared server-manifest tokens'
                      inputs:
                        sources: '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                        ifNoFilesFound: error
                      env:
                        FORCED_RESTART_VARIABLE: '$(Build.BuildId)'
                - ${{ if in(parameters.name, 'charge-server', 'history-server', 'subscription-server', 'pdf-server', 'ocpi-server', 'voucher-server', 'payments-bppay-server', 'map-server', 'anonymous-user-server', 'test-provider-server', 'gateway-private-server', 'gateway-public-server')}}:
                    - task: qetza.replacetokens.replacetokens-task.replacetokens@6
                      displayName: 'replace shared loadbalancer-manifest tokens'
                      inputs:
                        sources: '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                        ifNoFilesFound: error
                      env:
                        FORCED_RESTART_VARIABLE: '$(Build.BuildId)'
                - task: qetza.replacetokens.replacetokens-task.replacetokens@6
                  displayName: 'replace rollback manifest tokens'
                  inputs:
                    sources: |
                      $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml
                      $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/cloudwatch-analysis.yaml
                    ifNoFilesFound: error

            - ${{ if in(parameters.env, 'devops', 'pr', 'dev', 'test', 'performance', 'preprod', 'prod')}}:
                # Helm replacetokens
                - task: qetza.replacetokens.replacetokens-task.replacetokens@6
                  displayName: 'replace Helm values for ${{ parameters.server_name}}'
                  inputs:
                    sources: |
                      $(System.DefaultWorkingDirectory)/packages/${{ parameters.server_name}}/deployment/values.yaml
                      $(System.DefaultWorkingDirectory)/helm/common/values.yaml
                    ifNoFilesFound: error

            - script: |
                sed -i -e "s/\${image_tag}/${{ parameters.imageTag }}/" '${{ parameters.deploymentFilePath }}'
                sed -i -e 's/\${version_number}/${{ parameters.apiVersion }}/' '${{ parameters.deploymentFilePath }}'
                sed -i -e 's/\${aws_region}/$(AwsRegion)/' '${{ parameters.deploymentFilePath }}'
                sed -i -e "s/\${env}/${{ parameters.env }}/" '${{ parameters.deploymentFilePath }}'

                sed -i "s/\${version_number}/${{ parameters.apiVersion }}/g" $(System.DefaultWorkingDirectory)/packages/${{ parameters.server_name }}/deployment/values.yaml

                if [ "${{ parameters.env }}" == "us-uat" ]; then
                  sed -i -e "s/\${sub_domain}/.emsp-us-uat/" '${{ parameters.deploymentFilePath }}'
                fi
                sed -i -e "s/\${sub_domain}//" '${{ parameters.deploymentFilePath }}'

                if [ "${{ parameters.env }}" != "devops" ] && [ "${{ parameters.env }}" != "pr" ]; then
                  sed -i -e "s/\${parameters.server_name}/${{ parameters.name }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                  sed -i -e "s/\${version_number}/${{ parameters.apiVersion }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                  sed -i -e "s/\${targetPort}/${{ parameters.targetPort }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                  sed -i -e "s/\${deploymentNamespace}/${{ parameters.deploymentNamespace }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                  sed -i -e "s/\${deployName}/${{ parameters.deployName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'
                  sed -i -e "s/\${hpapdbName}/${{ parameters.hpapdbName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml'

                  if  [ "${{ parameters.env }}" != "pr" ] && [ "${{ parameters.env }}" != "dev" ] && [ "${{ parameters.env }}" != "test" ] && [ "${{ parameters.env }}" != "preprod" ] && [ "${{ parameters.env }}" != "performance" ]; then
                    sed -i -e "s/\${deployName}/${{ parameters.deployName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                    sed -i -e "s/\${hpapdbName}/${{ parameters.hpapdbName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                    sed -i -e "s/\${version_number}/${{ parameters.apiVersion }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                    sed -i -e "s/\${min_replicas}/${{ parameters.minReplicas }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                    sed -i -e "s/\${max_replicas}/${{ parameters.maxReplicas }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                    sed -i -e "s/\${average_utilization}/${{ parameters.averageUtilization }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml'
                  fi

                  if [ "${{ parameters.name }}" != "anonymous-user-server" ] && [ "${{ parameters.name }}" != "gateway-private-server" ] && [ "${{ parameters.name }}" != "gateway-public-server" ]; then
                    sed -i -e "s/\${parameters.server_name}/${{ parameters.name }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'
                    sed -i -e "s/\${version_number}/${{ parameters.apiVersion }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'
                    sed -i -e "s/\${targetPort}/${{ parameters.targetPort }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'
                    sed -i -e "s/\${deploymentNamespace}/${{ parameters.deploymentNamespace }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'
                    sed -i -e "s/\${deployName}/${{ parameters.deployName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'
                    sed -i -e "s/\${hpapdbName}/${{ parameters.hpapdbName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml'


                    if [ "${{ parameters.env }}" == "pr" ] || [ "${{ parameters.env }}" == "dev" ] || [ "${{ parameters.env }}" == "test" ] || [ "${{ parameters.env }}" == "preprod" ] || [ "${{ parameters.env }}" == "performance" ]; then
                      sed -i -e "s/\${deployName}/${{ parameters.deployName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                      sed -i -e "s/\${version_number}/${{ parameters.apiVersion }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'

                      sed -i -e "s/\${replicas}/${{ parameters.minReplicas }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                      sed -i -e "s/\${min_replicas}/${{ parameters.minReplicas }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                      sed -i -e "s/\${max_replicas}/${{ parameters.maxReplicas }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                      sed -i -e "s/\${hpapdbName}/${{ parameters.hpapdbName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                      sed -i -e "s/\${average_utilization}/${{ parameters.averageUtilization }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml'
                    fi
                  fi

                  if [ "${{ parameters.name }}" == "charge-server" ] || [ "${{ parameters.name }}" == "history-server" ] || [ "${{ parameters.name }}" == "pdf-server" ] || [ "${{ parameters.name }}" == "subscription-server" ] || [ "${{ parameters.name }}" == "ocpi-server" ] || [ "${{ parameters.name }}" == "voucher-server" ] || [ "${{ parameters.name }}" == "payments-bppay-server" ] || [ "${{ parameters.name }}" == "map-server" ] || [ "${{ parameters.name }}" == "anonymous-user-server" ] || [ "${{ parameters.name }}" == "test-provider-server" ] || [ "${{ parameters.name }}" == "gateway-private-server" ] || [ "${{ parameters.name }}" == "gateway-public-server" ]; then
                    sed -i -e "s/\${parameters.server_name}/${{ parameters.name }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${version_number}/${{ parameters.apiVersion }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${targetPort}/${{ parameters.targetPort }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${deploymentNamespace}/${{ parameters.deploymentNamespace }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${deployName}/${{ parameters.deployName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${hpapdbName}/${{ parameters.hpapdbName }}/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'
                    sed -i -e "s/\${loadBalancerTags}/elbv2.k8s.aws\/cluster=Pulse-Apps-Backend-${{ parameters.env }},kubernetes.io\/service-name=${{  parameters.deployName }}\/${{  parameters.deployName }},service.k8s.aws\/resource=LoadBalancer,kubernetes.io\/cluster\/Pulse-Apps-Backend-${{ parameters.env }}=owned/" '$(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml'

                  fi
                fi

              name: set_image_tag

            - task: S3Upload@1
              inputs:
                regionName: $(AwsRegion)
                bucketName: $(AwsLogicalAccountNameLowerCase)-microservice-deployment-${{ parameters.env }}
                sourceFolder: ${{ parameters.deploymentFolderPath }}
                globExpressions: '*.yaml'
                targetFolder: v${{ parameters.apiVersion }}/${{ parameters.featureName }}/$(Build.BuildId)
                createBucket: false
            - script: |
                cd $(pwd)/packages/${{parameters.featureName}}/src

                json_file="variables.json"
                secrets_ignore_file="secrets.ignore"

                secrets=()

                while IFS= read -r line || [ -n "$line" ]; do
                  secrets+=("$line")
                  echo "Processing line: $line"
                done < secrets.ignore

                echo "secrets is ${secrets[@]}"

                array_length=$(jq 'length' "$json_file")

                for ((i=0; i<$array_length; i++)); do
                  # Access the properties of each object
                  object_name=$(jq -r ".[$i].name" "$json_file")
                  object_source=$(jq -r ".[$i].source" "$json_file")
                  object_sourceName=$(jq -r ".[$i].sourceName" "$json_file")

                  ignore_secret=false

                  for ignored_secret in "${secrets[@]}"; do
                      if [ "$ignored_secret" == "$object_name" ]; then
                          ignore_secret=true
                          break
                      fi
                  done

                  if [[ $ignore_secret == true ]]; then
                    echo "Secret is in the secrets.ignore file. Skipping checks..."
                    continue
                  fi

                  if [ $object_source == 'aws-secrets-manager' ]; then
                    SECRET_VALUE=$(aws secretsmanager get-secret-value --secret-id ${{parameters.env}}-v${{parameters.apiVersion}}-$object_sourceName --query SecretString --output text --region $(AWSRegion))
                    SECRET_VALUE_STRING=$(echo "$SECRET_VALUE" | jq -r '.secret')

                    if [[ -z "$SECRET_VALUE_STRING" ]] || [[ "$SECRET_VALUE_STRING" == "placeholder" ]]; then
                      echo "❌ Error: Secret $object_sourceName is empty, has the value 'placeholder' or does not exist in AWS Secrets Manager for ${{parameters.env}} environment. Make sure this secret has a real value and try again!"
                      exit 1
                    else
                      echo "✅ Secret $object_sourceName exists in ${{parameters.env}} and has a valid value."
                    fi
                  elif [ $object_source == 'ado-variable-group' ]; then
                    if [[ -z "${!object_sourceName}" ]] || [[ "${!object_sourceName}" == "placeholder" ]]; then
                      echo "❌ Error: Variable $object_sourceName is empty, has the value 'placeholder' or does not exist in Azure Library Groups for ${{parameters.env}} environment. Make sure this variable has a real value and try again!"
                      exit 1
                    else
                      echo "✅ Variable $object_sourceName exists in Azure Library Groups for ${{parameters.env}} environment and has a valid value."
                    fi
                  fi
                done
              name: check_secrets_valid
              displayName: Check if secrets/variables are valid in ${{parameters.env}} for ${{parameters.featureName}}

            - ${{ if in(parameters.env, 'devops', 'pr', 'dev', 'test', 'performance', 'preprod', 'prod')}}:
                # Install Helm
                - task: HelmInstaller@1
                  displayName: 'Install Helm'
                  inputs:
                    helmVersionToInstall: 'latest'
                - script: |
                    cd $(System.DefaultWorkingDirectory)/helm/common
                    helm dependency update
                  displayName: 'Update Helm Dependencies'

                - script: |
                    aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
                    echo "##vso[task.setvariable variable=KUBECONFIG]$HOME/.kube/config"
                  displayName: Configure kubeconfig

                - script: |
                    echo "🧪 Debug: Checking Helm chart path"
                    ls -la $(System.DefaultWorkingDirectory)/helm/common
                    ls -la $(System.DefaultWorkingDirectory)/packages/${{ parameters.server_name }}/src
                  displayName: '🧪 Verify Helm chart exists'

                - script: |
                    echo "📄 Rendering Helm deployment file for preview..."

                    helm template "${{ parameters.deploymentName }}" \
                      "$(System.DefaultWorkingDirectory)/helm/common" \
                      -f "$(System.DefaultWorkingDirectory)/packages/${{ parameters.server_name }}/deployment/values.yaml" \
                      --set image.tag="${{ parameters.imageTag }}" \
                      --set environment="${{ parameters.env }}" \
                      --set shared.minReplicas="${{ parameters.minReplicas }}" \
                      --set shared.maxReplicas="${{ parameters.maxReplicas }}" \
                      --set shared.averageUtilization="${{ parameters.averageUtilization }}" \
                      --namespace "${{ parameters.deploymentNamespace }}" \
                      > rendered-${{ parameters.name }}.yaml

                    echo "✅ Rendered output:"
                    cat rendered-${{ parameters.name }}.yaml
                  displayName: '🧪 Preview Helm-rendered deployment YAML'
                # Deploy services using Helm (new step)
                - task: HelmDeploy@0
                  displayName: 'Deploy ${{ parameters.name }} services with Helm'
                  inputs:
                    command: upgrade
                    chartType: FilePath
                    chartPath: $(System.DefaultWorkingDirectory)/helm/common
                    valueFile: $(System.DefaultWorkingDirectory)/packages/${{ parameters.server_name }}/deployment/values.yaml
                    releaseName: ${{ parameters.name }}-${{ parameters.env }}
                    connectionType: 'None' # <-- Important! Tells the task not to use a service endpoint
                    kubeconfig: '$(KUBECONFIG)' # <-- Uses the kubeconfig generated in previous steps
                    overrideValues: |
                      versionNumber=${{ parameters.apiVersion }}
                      environment=${{ parameters.env }}
                      service.loadBalancer.tags=elbv2.k8s.aws\/cluster=Pulse-Apps-Backend-${{ parameters.env }},kubernetes.io\/service-name=${{  parameters.deployName }}\/${{  parameters.deployName }},service.k8s.aws\/resource=LoadBalancer,kubernetes.io\/cluster\/Pulse-Apps-Backend-${{ parameters.env }}=owned
                      image.tag=${{ parameters.imageTag }}
                    waitForExecution: true
                    arguments: --create-namespace --namespace ${{ parameters.deploymentNamespace }}
                - ${{ if and(ne(parameters.deploymentNamespace, 'anonymous-server'), ne(parameters.deploymentNamespace, 'gateway-server')) }}:
                    - script: |
                        kubectl argo rollouts status ${{ parameters.deployName }}-deployment-v${{ parameters.apiVersion }} -n ${{ parameters.deploymentNamespace }} --watch --timeout 600s
                      displayName: Check rollout status
                - script: |
                    echo "====== POD STATUS ======"
                    kubectl get pods -n ${{ parameters.deploymentNamespace }} -o wide

                    echo "====== NON-RUNNING PODS ======"
                    kubectl get pods -n ${{ parameters.deploymentNamespace }} --field-selector=status.phase!=Running -o name | while read pod; do
                      echo "Describing $pod..."
                      kubectl describe $pod -n ${{ parameters.deploymentNamespace }}
                      echo "Logs (last 20 lines)..."
                      kubectl logs $pod -n ${{ parameters.deploymentNamespace }} --tail=20 || true
                      echo "------------------------"
                    done

                    echo "====== EVENTS ======"
                    kubectl get events -n ${{ parameters.deploymentNamespace }} --sort-by=.lastTimestamp | tail -n 20
                  displayName: 'Debug Helm failure: show pod status and events'
                  condition: failed()
            - ${{ if notIn(parameters.env, 'devops', 'pr', 'dev', 'test', 'performance', 'preprod', 'prod')}}:
                - script: |
                    echo "##vso[task.setvariable variable=successfulDeployment]false"
                    aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
                    kubectl apply -f ${{ parameters.deploymentFilePath }} -n ${{ parameters.deploymentNamespace }} --record

                    kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/servers-manifest.yaml -n ${{ parameters.deploymentNamespace }} --record

                    if [ "${{ parameters.env }}" != "preprod" ]; then
                      kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/hpa.yaml -n ${{ parameters.deploymentNamespace }} --record
                    fi

                    if [ "${{ parameters.name }}" != "anonymous-user-server" ] && [ "${{ parameters.name }}" != "gateway-private-server" ] && [ "${{ parameters.name }}" != "gateway-public-server" ]; then
                      kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/cluster-ip-manifest.yaml -n ${{ parameters.deploymentNamespace }} --record
                      if [ "${{ parameters.env }}" == "preprod" ]; then
                        kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/rollout-manifest.yaml -n ${{ parameters.deploymentNamespace }} --record
                        kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/cloudwatch-analysis.yaml -n ${{ parameters.deploymentNamespace }} --record
                      fi
                    fi

                    if [ "${{ parameters.name }}" == "charge-server" ] || [ "${{ parameters.name }}" == "history-server" ] || [ "${{ parameters.name }}" == "pdf-server" ] || [ "${{ parameters.name }}" == "subscription-server" ] || [ "${{ parameters.name }}" == "ocpi-server" ] || [ "${{ parameters.name }}" == "voucher-server" ] || [ "${{ parameters.name }}" == "payments-bppay-server" ] || [ "${{ parameters.name }}" == "map-server" ] || [ "${{ parameters.name }}" == "anonymous-user-server" ] || [ "${{ parameters.name }}" == "test-provider-server" ] || [ "${{ parameters.name }}" == "gateway-private-server" ] || [ "${{ parameters.name }}" == "gateway-public-server" ]; then
                      kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/loadbalancer-manifest.yaml -n ${{ parameters.deploymentNamespace }} --record
                    fi

                    kubectl rollout status deployment/${{ parameters.deploymentName }} -n ${{ parameters.deploymentNamespace }}
                    if [ $? -ne 0 ]; then
                      exit 1
                    fi
                    kubectl get po -n ${{ parameters.deploymentNamespace }}

                    echo "##vso[task.setvariable variable=successfulDeployment]true"
                  name: deploy
                  displayName: Kubernetes Deployment
            - ${{ each gateway in parameters.gateways }}:
                - script: |
                    aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
                    kubectl rollout restart deployment/${{ gateway.deploymentName }}-v${{ parameters.apiVersion }} -n ${{ gateway.namespace }}
                  displayName: Restart ${{ gateway.deploymentName }}
            - ${{ if in(parameters.env, 'pr')}}:
                - template: ../tests/contract_tests/contract_tests.yaml
            - script: |
                echo "##vso[task.setvariable variable=successfulRollback]false"
                aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
                kubectl rollout undo deployment/${{ parameters.deploymentName }} -n ${{ parameters.deploymentNamespace }}
                kubectl get po -n ${{ parameters.deploymentNamespace }}
                echo "##vso[task.setvariable variable=successfulRollback]true"
              displayName: Rollback 🙃
              name: rollback
              condition: or(eq(variables.successfulDeployment, false),eq(variables.successfulContractTest, false), failed())
            - ${{ each gateway in parameters.gateways }}:
                - script: |
                    aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
                    kubectl rollout restart deployment/${{ gateway.deploymentName }}-v${{ parameters.apiVersion }} -n ${{ gateway.namespace }}
                  displayName: Restart ${{ gateway.deploymentName }}
                  condition: eq(variables.successfulRollback, true)
  - ${{ if in(parameters.env, 'pr')}}:
      - deployment: agent_cleanup
        pool: ContractTestsAgent
        environment: Pulse-Apps-Backend-${{ parameters.env }}
        displayName: Clean up AWS agent
        dependsOn: deploy_to_cluster
        condition: always()
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: none
                - template: ../tests/contract_tests/agent_cleanup.yaml
