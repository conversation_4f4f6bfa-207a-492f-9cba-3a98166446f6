import { DeactivateTagEvent } from './types/types';
import { TagDeactivationService } from './services/deactivation-service';
import {
    buildCsvLog,
    buildCsvRowsFromResults,
    buildCsvRowsFromEventError,
} from './utils/buildCsvLog';
import { logger } from './utils/logger';
import {
    validateDeactivateTagEvent,
    validateDeactivateTagEventArray,
} from './utils/input-validation';
import { runWithConcurrency } from './utils/concurrency';
import { env } from './env';
import { randomUUID } from 'crypto';

export const handler = async (
    event: DeactivateTagEvent | DeactivateTagEvent[],
): Promise<void> => {
    const deactivationService = new TagDeactivationService();
    const CONCURRENCY_LIMIT =
        Number(env.DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS) > 0
            ? Number(env.DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS)
            : 10;
    const csvLogId = randomUUID();
    logger.info(`Unique CSV Log ID: ${csvLogId}`);
    let allCsvRows: Array<{
        userId: string;
        tag: any;
        country: string;
        status: string;
        error?: string;
        csvLogId: string;
    }> = [];
    try {
        let validatedEvents: DeactivateTagEvent | DeactivateTagEvent[];
        if (Array.isArray(event)) {
            logger.info(
                `Validating batch of ${event.length} deactivation events`,
            );
            validatedEvents = validateDeactivateTagEventArray(event);
        } else {
            logger.info(
                `Validating single deactivation event for userId: ${event.userId}`,
            );
            validatedEvents = validateDeactivateTagEvent(event);
        }
        if (Array.isArray(validatedEvents)) {
            logger.info(
                `Processing batch of ${validatedEvents.length} deactivation events`,
            );
            const tasks = validatedEvents.map(
                (singleEvent, index) => async () => {
                    try {
                        const total = Array.isArray(validatedEvents)
                            ? validatedEvents.length
                            : 1;
                        logger.info(
                            `Processing event ${index + 1} of ${total}`,
                        );
                        const result =
                            await deactivationService.processDeactivationEvent(
                                singleEvent,
                            );
                        logger.info(
                            `Completed processing event ${
                                index + 1
                            } with status: ${
                                result.success ? 'success' : 'failed'
                            }`,
                        );
                        return {
                            eventIndex: index,
                            userId: singleEvent.userId,
                            tagCount: singleEvent.tagIds?.length || 0,
                            result,
                        };
                    } catch (error) {
                        logger.error(
                            `Failed to process event ${index + 1}: ${error}`,
                        );
                        return {
                            eventIndex: index,
                            userId: singleEvent.userId,
                            tagCount: singleEvent.tagIds?.length || 0,
                            error:
                                error instanceof Error
                                    ? error.message
                                    : String(error),
                            tags: singleEvent.tagIds || [],
                            country: singleEvent.country || '',
                        };
                    }
                },
            );
            const results = await runWithConcurrency(tasks, CONCURRENCY_LIMIT);
            logger.info(
                `Completed processing batch of ${validatedEvents.length} deactivation events`,
            );
            allCsvRows = buildCsvRowsFromResults(
                results,
                validatedEvents,
                csvLogId,
            );
        } else {
            logger.info(
                `Processing single deactivation event for userId: ${validatedEvents.userId}`,
            );
            const result = await deactivationService.processDeactivationEvent(
                validatedEvents,
            );
            logger.info(
                `Completed processing with status: ${
                    result.success ? 'success' : 'failed'
                }`,
            );
            const status = result.success ? 'success' : 'failed';
            let country = (validatedEvents as DeactivateTagEvent).country || '';
            const allTags = [
                ...(result.successfullyTerminated || []),
                ...(result.failedToTerminate || []),
            ];
            allTags.forEach((entry) => {
                const tag = entry.tag || {};
                const tagError = 'error' in entry ? entry.error : '';
                allCsvRows.push({
                    userId: (validatedEvents as DeactivateTagEvent).userId,
                    tag,
                    country,
                    status,
                    error: tagError,
                    csvLogId,
                });
            });
        }
    } catch (error) {
        allCsvRows = buildCsvRowsFromEventError(event, error, csvLogId);
        logger.error(
            `Input validation failed: ${
                error instanceof Error ? error.message : String(error)
            }`,
        );
    }
    logger.info(`CSV_RESULT\n${buildCsvLog(allCsvRows, csvLogId)}`);
    return;
};
