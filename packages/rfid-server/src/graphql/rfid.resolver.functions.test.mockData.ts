import { TagNotes, TagStatus } from '../common/enums';

export const fetchRFIDPayload = {
  rfidTableName: 'Name',
  rfidIndexName: 'Name',
  rfidIndexHash: 'Name',
  rfidIndexHashValue: 'Name',
  rfidIndexRange: 'Name',
  rfidIndexRangeValue: 'Name',
  exclusiveStartKey: 'Name',
};

export const deleteRfidPayload = {
  rfidTableName: 'rfid',
  rfidIndexName: 'rfid_card_number',
  rfidIndexValue: 'item',
};

export const incorrectDeleteRfidPayload = {
  rfidTableName: null,
  rfidIndexName: null,
  rfidIndexValue: null,
};

export const incorrectUpdateRFIDPayload = {
  rfidTableName: null,
  rfidIndexName: null,
  rfidIndexValue: null,
  rfidIndexHash: null,
  rfidIndexHashValue: null,
};

export const rfidResponsedata = {
  country: 'UK',
  postcode: 'NL33PT',
  first_name: '<PERSON>',
  last_name: 'Bloggs',
  date_added: '010203',
  user_id: '123',
  rfid_card_number: '123',
  address_line_1: '123',
  address_line_2: '123',
  address_line_3: '123',
  address_line_4: '123',
  address_line_5: '123',
  rfid_status: 'ACCEPTED',
};

export const requestSuccessRFIDPayloadNL = {
  country: 'NL',
  userId: 'dummyuserID',
  cardPreference: 'Visa',
  address: {
    addressLine: 'dummy',
    addressCity: 'dummy',
    addressPostcode: 'dummy',
    addressCountry: 'dummy',
  },
};

export const requestSuccessRFIDPayloadUK = {
  country: 'UK',
  userId: 'dummyuserID',
  cardPreference: 'Visa',
  address: {
    addressLine: 'dummy',
    addressCity: 'dummy',
    addressPostcode: 'dummy',
    addressCountry: 'dummy',
  },
};

export const addOrUnblockRFIDSuccessPayload = {
  userId: '12345',
  cardNumber: '7777',
  cardUid: 'test',
  country: 'DE',
};

export const blockRFIDSuccessPayload = {
  userId: '12345',
  country: 'DE',
  tagStatus: TagStatus.EXPIRED,
  cardUid: 'test',
  cardNumber: '7777',
};

export const incompletePayloadError = new Error('incomplete payload');

export const mockGetUserInfoSuccessContent = {
  balance: 0,
  roamingEnabled: true,
  country: 'NL',
  tagIds: [
    {
      tagCategoryName: 'HTB',
      tagTypeName: 'virtual',
      tagNotes: TagNotes.VIRTUAL_HTB,
      tagStatus: TagStatus.ACTIVE,
    },
    {
      tagCategoryName: 'DCS',
      tagTypeName: 'virtual',
      tagNotes: TagNotes.VIRTUAL_DCS,
      tagStatus: TagStatus.ACTIVE,
      tagCardNumber: 'test-card-number',
    },
  ],
  revenuePlanNames: ['5066', 'HEAVY'],
};

export const replaceRFIDSuccessPayload = {
  userId: '0050E000009RURI',
  country: 'UK',
};
