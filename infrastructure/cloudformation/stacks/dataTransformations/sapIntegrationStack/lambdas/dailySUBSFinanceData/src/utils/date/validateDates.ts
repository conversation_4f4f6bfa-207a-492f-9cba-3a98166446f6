export const validateAndNormalizeDates = (
  startDate: string,
  endDate: string,
): { startDate: string; endDate: string } => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime())) {
    throw new Error(`Invalid start date: ${startDate}`);
  }
  if (isNaN(end.getTime())) {
    throw new Error(`Invalid end date: ${endDate}`);
  }

  // The start date must always be the beginning of a new day
  start.setUTCHours(0, 0, 0, 0);

  // Checking if end date has non-zero time components (most common manual run pitfall)
  const endHours = end.getUTCHours();
  const endMinutes = end.getUTCMinutes();
  const endSeconds = end.getUTCSeconds();

  // Most common alternative scenario start date = end date but
  // start time = 00:00:00 and end time = 23:59:59
  if (endHours !== 0 || endMinutes !== 0 || endSeconds !== 0) {
    console.warn(
      `Invalid end date ${endDate}. ` +
        `Adjusting to next day at 00:00:00 to match expected behavior.`,
    );

    end.setUTCDate(end.getUTCDate() + 1);
    end.setUTCHours(0, 0, 0, 0);
  }

  if (end <= start) {
    throw new Error(
      `Invalid date range: endDate (${end.toISOString()}) must be after startDate (${start.toISOString()})`,
    );
  }

  const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
  if (daysDiff > 1) {
    console.log(`Large date range detected: ${daysDiff} days. `);
  }

  return {
    startDate: start.toUTCString(),
    endDate: end.toUTCString(),
  };
};
