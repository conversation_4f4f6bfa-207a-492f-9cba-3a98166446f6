{"name": "@bp/feature-payments-bppay-server", "version": "0.0.1", "private": true, "dependencies": {"@apollo/subgraph": "^2.1.3", "@bp/pulse-common": "^1.0.2", "apollo-opentracing": "^2.1.55", "apollo-server": "^3.10.2", "apollo-server-core": "^3.10.2", "apollo-server-errors": "^3.3.1", "apollo-server-express": "^3.4.0", "aws-sdk": "^2.1013.0", "axios": "^1.10.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "graphql": "^16.6.0", "graphql-constraint-directive": "^4.1.2", "graphql-middleware": "^6.1.33", "graphql-request": "^4.2.0", "graphql-shield": "^7.6.5", "helmet": "^4.6.0", "jaeger-client": "^3.18.1", "opentracing": "^0.14.5", "p-map": "^2.1.0", "schemaglue": "^4.3.0", "uuid": "^8.3.2", "winston": "^3.3.3"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/runtime": "^7.15.4", "@bp/eslint-plugin": "^0.0.3", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.0.9", "@types/compression": "^1.7.2", "@types/helmet": "^4.0.0", "@types/jaeger-client": "^3.18.3", "@types/jest": "^27.0.2", "@types/uuid": "^8.3.3", "babel-jest": "^27.3.1", "eslint": "^8.20.0", "jest": "^29.0.3", "nodemon": "^2.0.12", "prettier": "2.7.1", "ts-node": "^10.4.0", "typescript": "^4.3.5"}}