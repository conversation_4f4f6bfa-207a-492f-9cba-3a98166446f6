import { DeactivationCommandFactory } from '../commands/deactivation-factory';
import {
    DeactivateTagEvent,
    DeactivationResult,
    GetUserInfoResponse,
    Tag,
} from '../types/types';
import { TAG_NOTES } from '../utils/constants';
import { logger } from '../utils/logger';
import { getUserInfo } from './user';

export class TagDeactivationService {
    async processDeactivationEvent(
        event: DeactivateTagEvent,
    ): Promise<DeactivationResult> {
        if (!event.userId && (!event.tagIds || event.tagIds.length === 0)) {
            throw new Error('No user ID or tag IDs provided');
        }
        const { userId, tagIds, country: inputCountry } = event;
        if (!inputCountry) {
            throw new Error(`Country is required for ${userId}`);
        }
        const userData = await getUserInfo({ userId, country: inputCountry });
        const { country } = userData.userInfo;

        logger.info(
            `Processing deactivation for user ${userId}. Available tags from user info: ${JSON.stringify(
                userData.userInfo.tagIds,
            )} | Requested tagIds in payload: ${JSON.stringify(tagIds)}`,
        );

        const tags = this.getTagsToDeactivate(userId, tagIds, userData);

        if (
            userId &&
            tagIds.length > 0 &&
            userData.userInfo.tagIds?.length > 0
        ) {
            logger.info(
                `Warning: tagIds will be ignored in favour of the userId`,
            );
        }

        if (!tags.length) {
            logger.info(
                `No tags found to deactivate for user ${userId}. Check payload tagIds or user's available tags.`,
            );
            return {
                success: false,
                message: `There are no tags to deactivate for user ${userId}`,
                successfullyTerminated: [],
                failedToTerminate: [],
                balance: userData.userInfo.balance,
            };
        }

        logger.info(
            `Found ${tags.length} tags to deactivate: ${JSON.stringify(tags)}`,
        );

        return await this.deactivateTags(
            tags,
            userId,
            country,
            userData.userInfo.balance,
        );
    }

    private getTagsToDeactivate(
        userId: string | undefined,
        tagIds: Array<Tag>,
        user: GetUserInfoResponse,
    ): Array<Tag> {
        if (userId && user.userInfo.tagIds && user.userInfo.tagIds.length > 0) {
            return user.userInfo.tagIds.filter((tag) => {
                const tagNotesUpper = tag.tagNotes.toUpperCase();

                const isKnownTagType = Object.values(TAG_NOTES).includes(
                    tagNotesUpper as TAG_NOTES,
                );

                if (
                    tag.tagNotes === 'physical-RFID' &&
                    tag.tagStatus !== 'ACTIVE'
                ) {
                    return false;
                }

                return isKnownTagType;
            });
        }

        return tagIds || [];
    }

    private async deactivateTags(
        tags: Array<Tag>,
        userId: string | undefined,
        country: string,
        balance: number,
    ): Promise<DeactivationResult> {
        const successfullyTerminated = [];
        const failedToTerminate = [];

        for (const tag of tags) {
            try {
                const deactivationCommand =
                    DeactivationCommandFactory.createCommand(
                        tag,
                        userId,
                        country,
                    );

                const response = await deactivationCommand.execute();

                if (response.status === 200) {
                    successfullyTerminated.push({ tag, response });
                } else {
                    logger.error(
                        `Deactivation failed for ${
                            tag.tagNotes
                        } tag for user ${userId}. Status: ${
                            response.status
                        }, Response: ${JSON.stringify(response)}`,
                    );
                    failedToTerminate.push({ tag, response });
                }
            } catch (error) {
                logger.error(
                    `There has been an error while deactivating the ${tag.tagNotes} tag for user ${userId}: ${error}`,
                );
                failedToTerminate.push({ tag, error });
            }
        }

        return {
            success: !failedToTerminate.length,
            successfullyTerminated,
            failedToTerminate,
            balance,
        };
    }
}
