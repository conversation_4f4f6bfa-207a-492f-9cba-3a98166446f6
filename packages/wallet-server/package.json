{"name": "@bp/feature-wallet-server", "version": "0.0.1", "private": true, "main": "./dist/index.js", "dependencies": {"@apollo/subgraph": "^2.7.1", "@bp/pulse-common": "^1.0.2", "apollo-opentracing": "^2.1.31", "apollo-server": "^3.10.2", "apollo-server-core": "^3.3.0", "apollo-server-express": "^3.3.0", "aws-sdk": "^2.1018.0", "axios": "^1.10.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "graphql": "^16.6.0", "graphql-constraint-directive": "^4.1.2", "graphql-middleware": "^6.1.33", "graphql-rate-limit": "^3.3.0", "graphql-request": "^3.5.0", "graphql-shield": "^7.6.5", "graphql-tag": "^2.12.6", "helmet": "^4.6.0", "ioredis": "^4.17.3", "jaeger-client": "^3.18.1", "opentracing": "^0.14.5", "schemaglue": "^4.1.0", "uuid": "^8.3.2", "winston": "^3.3.3"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/runtime": "^7.14.6", "@bp/eslint-plugin": "^0.0.3", "@types/compression": "^1.7.1", "@types/cors": "^2.8.12", "@types/ioredis": "^4.26.5", "@types/jaeger-client": "^3.18.2", "@types/jest": "^26.0.24", "@types/uuid": "^8.3.3", "@typescript-eslint/eslint-plugin": "^5.31.0", "@typescript-eslint/parser": "^5.31.0", "babel-jest": "^29.7.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-sonarjs": "^0.13.0", "eslint-plugin-you-dont-need-lodash-underscore": "^6.12.0", "jest": "^29.7.0", "nodemon": "^2.0.12", "prettier": "2.7.1", "ts-jest": "^29.4.0", "ts-node": "^10.2.1", "typescript": "^4.9.5"}}