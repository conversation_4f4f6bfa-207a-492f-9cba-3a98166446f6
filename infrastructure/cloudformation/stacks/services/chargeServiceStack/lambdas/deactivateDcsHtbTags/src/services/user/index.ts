import { request } from 'graphql-request';
import { GET_USER_INFO, UPDATE_TAG_INTERNAL } from './queries';
import { GetUserInfoResponse } from '../../types/types.js';
import { env } from './../../env';
import {
    QueryDefaultResponse,
    UpdateTagInternalInput,
} from '../../utils/constants';
import { logger } from '../../utils/logger';

// Internal service config required to make calls to our micro-services, the secret should match the value set
// in the private Apollo Gateway servers environment variables
export const getUserInfo = async ({
    userId,
    country,
}: {
    userId: string;
    country: string;
}): Promise<GetUserInfoResponse> =>
    request(
        env.PRIVATE_GATEWAY_SERVER_HTTP,
        GET_USER_INFO,
        {
            userId,
            appCountry: country,
        },
        { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
    )
        .then(({ userInfo }: GetUserInfoResponse) => ({ userInfo }))
        .catch((error) => {
            logger.error(`Could not get user info: ${error.message}`);
            throw new Error(`message: ${error.message}`);
        });

export const updateTagInternal = async ({
    salesforceId,
    tagCardNumber,
    country,
    tagBarredDatetime,
    tagStatus,
}: UpdateTagInternalInput) =>
    request(
        env.PRIVATE_GATEWAY_SERVER_HTTP,
        UPDATE_TAG_INTERNAL,
        {
            payload: {
                salesforceId,
                tagCardNumber,
                country,
                tagBarredDatetime,
                tagStatus,
            },
        },
        { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
    )
        .then(
            (queryResponse: { updateTagInternal: QueryDefaultResponse }) =>
                queryResponse.updateTagInternal,
        )
        .catch((error: any) => {
            logger.error(
                `Error updating tag ${tagCardNumber} for user ${salesforceId}: ${error}`,
            );
            throw new Error(`message: ${error.message}`);
        });
