/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
  '\n  mutation capturePayment(\n    $paymentId: String\n    $amount: Float\n    $currency: String\n    $appCountry: String\n    $userId: String\n    $userType: String\n    $chargeSessionId: String\n  ) {\n    capturePayment(\n      paymentId: $paymentId\n      amount: $amount\n      currency: $currency\n      appCountry: $appCountry\n      userId: $userId\n      userType: $userType\n      chargeSessionId: $chargeSessionId\n    )\n  }\n': typeof types.CapturePaymentDocument;
  '\n  mutation createOrder(\n    $userId: String\n    $appCountry: String\n    $paymentId: String\n    $chargeSessionId: String\n  ) {\n    createOrder(\n      userId: $userId\n      appCountry: $appCountry\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n    ) {\n      paymentId\n      correlationId\n    }\n  }\n': typeof types.CreateOrderDocument;
  '\n  mutation makeSimplePayment(\n    $userId: String!\n    $paymentId: String!\n    $chargeSessionId: String\n    $correlationId: String!\n    $paymentMethodId: String!\n    $appCountry: String!\n    $amount: Float!\n    $currency: String!\n    $outstanding: Boolean\n    $operationId: String\n  ) {\n    makeSimplePayment(\n      userId: $userId\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n      correlationId: $correlationId\n      paymentMethodId: $paymentMethodId\n      appCountry: $appCountry\n      amount: $amount\n      currency: $currency\n      outstanding: $outstanding\n      operationId: $operationId\n    ) {\n      status\n    }\n  }\n': typeof types.MakeSimplePaymentDocument;
  '\n  mutation createPaymentRecordInternal(\n    $chargeSessionId: String!\n    $amount: Float\n    $currency: String!\n    $userId: String!\n    $paymentId: String!\n    $finalPaymentStatus: String\n    $skipStoringPaymentRecord: Boolean\n  ) {\n    createPaymentRecordInternal(\n      chargeSessionId: $chargeSessionId\n      amount: $amount\n      currency: $currency\n      userId: $userId\n      paymentId: $paymentId\n      finalPaymentStatus: $finalPaymentStatus\n      skipStoringPaymentRecord: $skipStoringPaymentRecord\n    )\n  }\n': typeof types.CreatePaymentRecordInternalDocument;
  '\n  query getPaymentRecord($paymentId: String!) {\n    getPaymentRecord(paymentId: $paymentId) {\n      status\n      message\n      paymentId\n      chargeSessionId\n      userId\n      preAuthAmount\n      preAuthStatus\n      transactionId\n      transactionNumber\n      finalPaymentStatus\n      paymentMethodType\n      orderStarted\n      voidTransaction\n    }\n  }\n': typeof types.GetPaymentRecordDocument;
  '\n  mutation voidOrder($paymentData: [PaymentData!]!) {\n    voidOrder(paymentData: $paymentData) {\n      message\n      status\n      transactions {\n        paymentId\n        isVoided\n      }\n    }\n  }\n': typeof types.VoidOrderDocument;
};
const documents: Documents = {
  '\n  mutation capturePayment(\n    $paymentId: String\n    $amount: Float\n    $currency: String\n    $appCountry: String\n    $userId: String\n    $userType: String\n    $chargeSessionId: String\n  ) {\n    capturePayment(\n      paymentId: $paymentId\n      amount: $amount\n      currency: $currency\n      appCountry: $appCountry\n      userId: $userId\n      userType: $userType\n      chargeSessionId: $chargeSessionId\n    )\n  }\n':
    types.CapturePaymentDocument,
  '\n  mutation createOrder(\n    $userId: String\n    $appCountry: String\n    $paymentId: String\n    $chargeSessionId: String\n  ) {\n    createOrder(\n      userId: $userId\n      appCountry: $appCountry\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n    ) {\n      paymentId\n      correlationId\n    }\n  }\n':
    types.CreateOrderDocument,
  '\n  mutation makeSimplePayment(\n    $userId: String!\n    $paymentId: String!\n    $chargeSessionId: String\n    $correlationId: String!\n    $paymentMethodId: String!\n    $appCountry: String!\n    $amount: Float!\n    $currency: String!\n    $outstanding: Boolean\n    $operationId: String\n  ) {\n    makeSimplePayment(\n      userId: $userId\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n      correlationId: $correlationId\n      paymentMethodId: $paymentMethodId\n      appCountry: $appCountry\n      amount: $amount\n      currency: $currency\n      outstanding: $outstanding\n      operationId: $operationId\n    ) {\n      status\n    }\n  }\n':
    types.MakeSimplePaymentDocument,
  '\n  mutation createPaymentRecordInternal(\n    $chargeSessionId: String!\n    $amount: Float\n    $currency: String!\n    $userId: String!\n    $paymentId: String!\n    $finalPaymentStatus: String\n    $skipStoringPaymentRecord: Boolean\n  ) {\n    createPaymentRecordInternal(\n      chargeSessionId: $chargeSessionId\n      amount: $amount\n      currency: $currency\n      userId: $userId\n      paymentId: $paymentId\n      finalPaymentStatus: $finalPaymentStatus\n      skipStoringPaymentRecord: $skipStoringPaymentRecord\n    )\n  }\n':
    types.CreatePaymentRecordInternalDocument,
  '\n  query getPaymentRecord($paymentId: String!) {\n    getPaymentRecord(paymentId: $paymentId) {\n      status\n      message\n      paymentId\n      chargeSessionId\n      userId\n      preAuthAmount\n      preAuthStatus\n      transactionId\n      transactionNumber\n      finalPaymentStatus\n      paymentMethodType\n      orderStarted\n      voidTransaction\n    }\n  }\n':
    types.GetPaymentRecordDocument,
  '\n  mutation voidOrder($paymentData: [PaymentData!]!) {\n    voidOrder(paymentData: $paymentData) {\n      message\n      status\n      transactions {\n        paymentId\n        isVoided\n      }\n    }\n  }\n':
    types.VoidOrderDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  mutation capturePayment(\n    $paymentId: String\n    $amount: Float\n    $currency: String\n    $appCountry: String\n    $userId: String\n    $userType: String\n    $chargeSessionId: String\n  ) {\n    capturePayment(\n      paymentId: $paymentId\n      amount: $amount\n      currency: $currency\n      appCountry: $appCountry\n      userId: $userId\n      userType: $userType\n      chargeSessionId: $chargeSessionId\n    )\n  }\n',
): typeof documents['\n  mutation capturePayment(\n    $paymentId: String\n    $amount: Float\n    $currency: String\n    $appCountry: String\n    $userId: String\n    $userType: String\n    $chargeSessionId: String\n  ) {\n    capturePayment(\n      paymentId: $paymentId\n      amount: $amount\n      currency: $currency\n      appCountry: $appCountry\n      userId: $userId\n      userType: $userType\n      chargeSessionId: $chargeSessionId\n    )\n  }\n'];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  mutation createOrder(\n    $userId: String\n    $appCountry: String\n    $paymentId: String\n    $chargeSessionId: String\n  ) {\n    createOrder(\n      userId: $userId\n      appCountry: $appCountry\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n    ) {\n      paymentId\n      correlationId\n    }\n  }\n',
): typeof documents['\n  mutation createOrder(\n    $userId: String\n    $appCountry: String\n    $paymentId: String\n    $chargeSessionId: String\n  ) {\n    createOrder(\n      userId: $userId\n      appCountry: $appCountry\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n    ) {\n      paymentId\n      correlationId\n    }\n  }\n'];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  mutation makeSimplePayment(\n    $userId: String!\n    $paymentId: String!\n    $chargeSessionId: String\n    $correlationId: String!\n    $paymentMethodId: String!\n    $appCountry: String!\n    $amount: Float!\n    $currency: String!\n    $outstanding: Boolean\n    $operationId: String\n  ) {\n    makeSimplePayment(\n      userId: $userId\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n      correlationId: $correlationId\n      paymentMethodId: $paymentMethodId\n      appCountry: $appCountry\n      amount: $amount\n      currency: $currency\n      outstanding: $outstanding\n      operationId: $operationId\n    ) {\n      status\n    }\n  }\n',
): typeof documents['\n  mutation makeSimplePayment(\n    $userId: String!\n    $paymentId: String!\n    $chargeSessionId: String\n    $correlationId: String!\n    $paymentMethodId: String!\n    $appCountry: String!\n    $amount: Float!\n    $currency: String!\n    $outstanding: Boolean\n    $operationId: String\n  ) {\n    makeSimplePayment(\n      userId: $userId\n      paymentId: $paymentId\n      chargeSessionId: $chargeSessionId\n      correlationId: $correlationId\n      paymentMethodId: $paymentMethodId\n      appCountry: $appCountry\n      amount: $amount\n      currency: $currency\n      outstanding: $outstanding\n      operationId: $operationId\n    ) {\n      status\n    }\n  }\n'];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  mutation createPaymentRecordInternal(\n    $chargeSessionId: String!\n    $amount: Float\n    $currency: String!\n    $userId: String!\n    $paymentId: String!\n    $finalPaymentStatus: String\n    $skipStoringPaymentRecord: Boolean\n  ) {\n    createPaymentRecordInternal(\n      chargeSessionId: $chargeSessionId\n      amount: $amount\n      currency: $currency\n      userId: $userId\n      paymentId: $paymentId\n      finalPaymentStatus: $finalPaymentStatus\n      skipStoringPaymentRecord: $skipStoringPaymentRecord\n    )\n  }\n',
): typeof documents['\n  mutation createPaymentRecordInternal(\n    $chargeSessionId: String!\n    $amount: Float\n    $currency: String!\n    $userId: String!\n    $paymentId: String!\n    $finalPaymentStatus: String\n    $skipStoringPaymentRecord: Boolean\n  ) {\n    createPaymentRecordInternal(\n      chargeSessionId: $chargeSessionId\n      amount: $amount\n      currency: $currency\n      userId: $userId\n      paymentId: $paymentId\n      finalPaymentStatus: $finalPaymentStatus\n      skipStoringPaymentRecord: $skipStoringPaymentRecord\n    )\n  }\n'];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  query getPaymentRecord($paymentId: String!) {\n    getPaymentRecord(paymentId: $paymentId) {\n      status\n      message\n      paymentId\n      chargeSessionId\n      userId\n      preAuthAmount\n      preAuthStatus\n      transactionId\n      transactionNumber\n      finalPaymentStatus\n      paymentMethodType\n      orderStarted\n      voidTransaction\n    }\n  }\n',
): typeof documents['\n  query getPaymentRecord($paymentId: String!) {\n    getPaymentRecord(paymentId: $paymentId) {\n      status\n      message\n      paymentId\n      chargeSessionId\n      userId\n      preAuthAmount\n      preAuthStatus\n      transactionId\n      transactionNumber\n      finalPaymentStatus\n      paymentMethodType\n      orderStarted\n      voidTransaction\n    }\n  }\n'];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: '\n  mutation voidOrder($paymentData: [PaymentData!]!) {\n    voidOrder(paymentData: $paymentData) {\n      message\n      status\n      transactions {\n        paymentId\n        isVoided\n      }\n    }\n  }\n',
): typeof documents['\n  mutation voidOrder($paymentData: [PaymentData!]!) {\n    voidOrder(paymentData: $paymentData) {\n      message\n      status\n      transactions {\n        paymentId\n        isVoided\n      }\n    }\n  }\n'];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> =
  TDocumentNode extends DocumentNode<infer TType, any> ? TType : never;
