import dotenv from 'dotenv';

dotenv.config({ path: `${__dirname}/../.env` });

const requiredEnvVars = [
    'NODE_ENV',
    'DCS_TOKEN_URL',
    'DCS_TOKEN_CLIENT_ID',
    'DCS_TOKEN_CLIENT_SECRET',
    'DCS_TOKEN_RESOURCE',
    'DCS_SUBSCRIPTION_KEY',
    'DCS_FLEET_SERVICES_URL',
    'DCS_FLEET_ACCESS_KEY',
    'NL_FLEET_GROUP',
    'DE_FLEET_GROUP',
    'ES_FLEET_GROUP',
    'HTB_SERVICES_REST',
    'HTB_SERVICES_AUTH',
    'HTB_X_API_KEY',
    'HTB_AUTH_TOKEN',
    'HTB_SERVICES_USERNAME_NL',
    'HTB_SERVICES_PASSWORD_NL',
    'HTB_SERVICES_USERNAME_DE',
    'HTB_SERVICES_PASSWORD_DE',
    'HTB_SERVICES_USERNAME_ES',
    'HTB_SERVICES_PASSWORD_ES',
    'PRIVATE_GATEWAY_SERVER_HTTP',
    'APOLLO_INTERNAL_SECRET',
    'DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS',
];

const missingVars = requiredEnvVars.filter(
    (variable) => process.env[variable] === undefined,
);

if (missingVars.length > 0) {
    console.log('Aborting environment variables missing: ', missingVars);
    if (process.env.NODE_ENV !== 'test') {
        process.exit(-1);
    }
}

const {
    NODE_ENV,
    DCS_TOKEN_URL,
    DCS_TOKEN_CLIENT_ID,
    DCS_TOKEN_CLIENT_SECRET,
    DCS_TOKEN_RESOURCE,
    DCS_SUBSCRIPTION_KEY,
    DCS_FLEET_SERVICES_URL,
    DCS_FLEET_ACCESS_KEY,
    NL_FLEET_GROUP,
    DE_FLEET_GROUP,
    ES_FLEET_GROUP,
    HTB_SERVICES_REST,
    HTB_SERVICES_AUTH,
    HTB_X_API_KEY,
    HTB_AUTH_TOKEN,
    HTB_SERVICES_USERNAME_NL,
    HTB_SERVICES_PASSWORD_NL,
    HTB_SERVICES_USERNAME_DE,
    HTB_SERVICES_PASSWORD_DE,
    HTB_SERVICES_USERNAME_ES,
    HTB_SERVICES_PASSWORD_ES,
    PRIVATE_GATEWAY_SERVER_HTTP,
    APOLLO_INTERNAL_SECRET,
    DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS,
} = process.env;

const AVAILABLE_ENV_VARS = {
    NODE_ENV,
    DCS_TOKEN_URL,
    DCS_TOKEN_CLIENT_ID,
    DCS_TOKEN_CLIENT_SECRET,
    DCS_TOKEN_RESOURCE,
    DCS_SUBSCRIPTION_KEY,
    DCS_FLEET_SERVICES_URL,
    DCS_FLEET_ACCESS_KEY,
    NL_FLEET_GROUP,
    DE_FLEET_GROUP,
    ES_FLEET_GROUP,
    HTB_SERVICES_REST,
    HTB_SERVICES_AUTH,
    HTB_X_API_KEY,
    HTB_AUTH_TOKEN,
    HTB_SERVICES_USERNAME_NL,
    HTB_SERVICES_PASSWORD_NL,
    HTB_SERVICES_USERNAME_DE,
    HTB_SERVICES_PASSWORD_DE,
    HTB_SERVICES_USERNAME_ES,
    HTB_SERVICES_PASSWORD_ES,
    PRIVATE_GATEWAY_SERVER_HTTP,
    APOLLO_INTERNAL_SECRET,
    DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS,
};

export type EnvVars = Record<keyof typeof AVAILABLE_ENV_VARS, string>;

export const env = AVAILABLE_ENV_VARS as EnvVars;
