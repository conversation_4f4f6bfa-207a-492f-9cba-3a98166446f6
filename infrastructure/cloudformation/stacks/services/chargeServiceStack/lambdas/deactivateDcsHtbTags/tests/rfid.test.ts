import { blockRFID } from '../src/services/rfid';
import { request } from 'graphql-request';
import { logger } from '../src/utils/logger';
import { BLOCK_RFID } from '../src/services/rfid/queries';

jest.mock('graphql-request');
jest.mock('../src/env', () => ({
    env: require('./mocks/env.mock').env,
}));
jest.mock('../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

jest.mock('../src/services/rfid/queries', () => ({
    BLOCK_RFID: 'mock_block_rfid_query',
}));

describe('RFID Service', () => {
    const mockBlockRFIDInput = {
        userId: 'user-123',
        country: 'NL',
        reasonForBlocking: 'Request to Cancel',
        cardUid: '12345',
        cardNumber: 'CARD123',
    };

    const mockSuccessResponse = {
        blockRFID: {
            status: 200,
            message: 'RFID blocked successfully',
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should successfully block an RFID card', async () => {
        (request as jest.Mock).mockResolvedValue(mockSuccessResponse);

        const result = await blockRFID(mockBlockRFIDInput);

        expect(request).toHaveBeenCalledWith(
            'https://mock-private-gateway',
            BLOCK_RFID,
            mockBlockRFIDInput,
            { 'x-apollo-internal-secret': 'mock-internal-secret' },
        );

        expect(result).toEqual(mockSuccessResponse.blockRFID);
    });

    it('should handle errors when blocking RFID fails', async () => {
        const mockError = new Error('GraphQL request failed');
        (request as jest.Mock).mockRejectedValue(mockError);

        await expect(blockRFID(mockBlockRFIDInput)).rejects.toThrow(
            'message: GraphQL request failed',
        );

        expect(logger.error).toHaveBeenCalledWith(
            expect.stringContaining('Could not block RFID for user user-123'),
        );
    });

    it('should include all required fields in the request', async () => {
        (request as jest.Mock).mockResolvedValue(mockSuccessResponse);

        await blockRFID(mockBlockRFIDInput);

        const requestCall = (request as jest.Mock).mock.calls[0];
        const variables = requestCall[2];

        expect(variables).toHaveProperty('userId', 'user-123');
        expect(variables).toHaveProperty('country', 'NL');
        expect(variables).toHaveProperty(
            'reasonForBlocking',
            'Request to Cancel',
        );
        expect(variables).toHaveProperty('cardUid', '12345');
        expect(variables).toHaveProperty('cardNumber', 'CARD123');
    });
});
