export async function runWithConcurrency<T>(
    tasks: (() => Promise<T>)[],
    limit: number,
): Promise<T[]> {
    const results: T[] = [];
    let i = 0;
    async function next(): Promise<void> {
        if (i >= tasks.length) return;
        const current = i++;
        results[current] = await tasks[current]();
        await next();
    }
    const runners = Array.from({ length: Math.min(limit, tasks.length) }, () =>
        next(),
    );
    await Promise.all(runners);
    return results;
}
