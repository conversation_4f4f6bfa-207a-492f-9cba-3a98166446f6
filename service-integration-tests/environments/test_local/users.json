[{"testScope": "loginEmail", "firstName": "loginEmail", "lastName": "Last", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "email": "<EMAIL>", "type": "SUBS-WALLET"}, {"testScope": "registerUser_UK", "firstName": "United", "lastName": "Kingdom", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "3527248447", "email": "<EMAIL>"}, {"testScope": "registerUser_DE", "firstName": "<PERSON>", "lastName": "Once", "country": "Germany", "countryCode": "DE", "phonePrefix": "+1", "phoneNumber": "6295002803", "email": "<EMAIL>"}, {"testScope": "charge_UK", "firstName": "ChargerUK", "lastName": "Tester", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "chargepoints": ["24777"], "email": "<EMAIL>", "type": "PAYG-Wallet", "paymentAuthType": "PREAUTH"}, {"testScope": "charge_UK_preauth", "firstName": "ChargerUK", "lastName": "Tester", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "chargepoints": ["24777"], "email": "<EMAIL>", "type": "PAYG-Wallet", "paymentAuthType": "PREAUTH"}, {"testScope": "charge_ES", "firstName": "ChargerES", "lastName": "Tester", "country": "Spain", "countryCode": "ES", "street": "Apartado de Correos", "houseNumber": "1", "postalCode": "37450", "town": "Madrid", "timeZone": "Europe/Madrid", "chargepoints": ["ES*ION*E412101"], "email": "<EMAIL>", "type": "PAYG-Wallet", "paymentAuthType": "MIT"}, {"testScope": "charge_DE", "firstName": "German", "lastName": "<PERSON>", "country": "Germany", "countryCode": "DE", "street": "Hohe", "houseNumber": "11", "postalCode": "14195", "town": "Berlin", "timeZone": "Europe/Berlin", "chargepoints": ["DE*BPE*E0F460*01"], "email": "<EMAIL>", "type": "PAYG-Wallet", "paymentAuthType": "PREAUTH"}, {"testScope": "chargeWithoutCard", "firstName": "ChargeNoCard", "lastName": "Tester", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "6063571184", "chargepoints": ["24777"], "email": "<EMAIL>"}, {"testScope": "addPaymentMethod", "firstName": "addPaymentMethod", "lastName": "Tester", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "5853022316", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "changeDefaultPayMethod", "firstName": "Change Default", "lastName": "Method", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "3605050062", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "personalInformation", "firstName": "Charger Test", "lastName": "B", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "8159120072", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "changeEmail", "firstName": "Emailer", "lastName": "<PERSON>", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+1", "phoneNumber": "7248603482", "type": "PAYG-Wallet"}, {"testScope": "changePhone", "firstName": "phone", "lastName": "changer", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "email": "<EMAIL>", "phone1": "+447723445970", "phone2": "+17178040908", "type": "PAYG-Wallet"}, {"testScope": "marketingPreferences", "firstName": "Market", "lastName": "Pref", "country": "United Kingdom", "countryCode": "UK", "phonePrefix": "+1", "phoneNumber": "7244122278", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "favourite", "firstName": "Favorite", "lastName": "cp", "country": "Germany", "countryCode": "DE", "street": "Hohe", "houseNumber": "11", "postalCode": "14195", "town": "Berlin", "timeZone": "Europe/Berlin", "phonePrefix": "+44", "phoneNumber": "97077777777", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "addRfid", "firstName": "Rfid", "lastName": "Card", "country": "Germany", "countryCode": "DE", "street": "Hohe", "houseNumber": "11", "postalCode": "14195", "town": "Berlin", "timeZone": "Europe/Berlin", "email": "<EMAIL>", "type": "PAYG-Wallet"}, {"testScope": "subscription", "firstName": "Subs", "lastName": "Scribe", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "email": "<EMAIL>"}, {"testScope": "delete_UK", "firstName": "Delete", "lastName": "Me", "country": "United Kingdom", "countryCode": "UK", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "email": "<EMAIL>"}]