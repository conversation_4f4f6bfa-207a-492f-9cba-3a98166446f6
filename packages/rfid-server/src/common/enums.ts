import { PartnerType as SupportedPartners } from '@bp/pulse-shared-types/lib/enums/PartnerType';

export { SupportedPartners };

export enum ReasonForBlocking {
  'NO_CREDIT' = 'No Credit',
  'REQUEST_TO_BLOCK' = 'Request to Block',
  'REQUEST_TO_CANCEL' = 'Request to Cancel',
}

export enum TagStatus {
  ACTIVE = 'ACTIVE',
  NO_CREDIT = 'NO_CREDIT',
  BLOCKED = 'BLOCKED',
  PENDING_TERMINATION = 'PENDING_TERMINATION',
  CUSTOMER_REQUESTED = 'CUSTOMER_REQUESTED',
  SUPPLIER_REQUESTED = 'SUPPLIER_REQUESTED',
  SUPPLIER_ACCEPTED = 'SUPPLIER_ACCEPTED',
  SUPPLIER_FAILED = 'SUPPLIER_FAILED',
  EXPIRED = 'EXPIRED',
  SUPPLIER_REJECTED = 'SUPPLIER_REJECTED',
  ERROR = 'ERROR',
  FIRST = 'FIRST',
  TERMINATED = 'TERMINATED',
}

export enum TagNotes {
  VIRTUAL_HTB = 'virtual-HTB',
  VIRTUAL_DCS = 'virtual-DCS',
  VIRTUAL_CV_PAYG = 'virtual-CV-PAYG',
  PHYSICAL_RFID = 'physical-RFID',
}

export enum TagStatusValidForUpdate {
  ACTIVE = 'ACTIVE',
  NO_CREDIT = 'NO_CREDIT',
  BLOCKED = 'BLOCKED',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  DELETED = 'DELETED',
}

export enum UserTypes {
  GUEST = 'guest',
  PAYG = 'PAYG',
  SUBS = 'SUBS',
  SUBSCRIBER = 'Subscriber',
  PAYG_Wallet = 'PAYG-Wallet',
  SUBS_Wallet = 'SUBS-WALLET',
  UBER = 'UBER',
}

export enum Countries {
  UK = 'UK',
  NL = 'NL',
  DE = 'DE',
}

export enum ChargeProviders {
  DCS = 'DCS',
  HTB = 'HTB',
  CV_PAYG = 'CV-PAYG',
}

export enum Type {
  RFID = 'RFID',
  OTHER = 'OTHER',
}
