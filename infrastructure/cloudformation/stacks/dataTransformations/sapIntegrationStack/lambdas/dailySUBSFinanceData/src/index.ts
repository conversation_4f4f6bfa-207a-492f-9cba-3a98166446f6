import type { Handler } from 'aws-lambda';
import { DE_SUBS_FINANCE_SUPPORT } from './env/env';
import {
  MergedSubscriptionRecord,
  PaymentStatus,
} from './models/SubsTransaction';
import { getAllSubsTransactions } from './services/getAllSubsTransactions/getAllSubsTransactions';
import { getReceiptDetails } from './services/getReceiptDetail/getReceiptDetail';
import { uploadCSV } from './utils/csv/csv';
import { getTodayStart, getYesterdayStart } from './utils/date/date';
import { logger } from './utils/logger/logger';
import { mergeSubscriptionRecord } from './utils/transformations/dataMerge';
import { validateAllSubsTransactionsForMissingFields } from './utils/validation/validation';
import { validateAndNormalizeDates } from './utils/date/validateDates';

export const handler: Handler = async (event, _, _callback: () => void) => {
  try {
    // Dynamically choose provided dates or default to yesterday's range
    let startDate = event?.startDate
      ? new Date(event.startDate)
      : getYesterdayStart();
    let endDate = event?.endDate ? new Date(event.endDate) : getTodayStart();

    // Dates validation and normalization for keeping a standard input form
    if (event?.startDate && event?.endDate) {
      const normalized = validateAndNormalizeDates(
        event.startDate,
        event.endDate,
      );
      startDate = new Date(normalized.startDate);
      endDate = new Date(normalized.endDate);
    }

    logger.info(
      `Processing sub transactions from ${startDate.toISOString()} to ${endDate.toISOString()}`,
    );

    const allSubsTransactions = await getAllSubsTransactions(
      startDate.toISOString(),
      endDate.toISOString(),
      PaymentStatus.CAPTURED,
    );
    const trialTransactions =
      DE_SUBS_FINANCE_SUPPORT === 'false'
        ? []
        : allSubsTransactions.filter((el) => el.isTrial);
    const nonTrialTransactions = allSubsTransactions.filter(
      (el) => !el.isTrial,
    );

    // Fetch receipt details only for non-trial transactions
    const transactionsIds = nonTrialTransactions.map((el) => el.transactionId);
    const receiptDetails = await getReceiptDetails(transactionsIds, false);

    const mergedNonTrialTransactions = mergeSubscriptionRecord(
      nonTrialTransactions,
      receiptDetails.getReceiptDetail.receiptDetail,
    );

    const mergedTrialTransaction: MergedSubscriptionRecord[] =
      trialTransactions.map((item) => {
        return { ...item, receipt_id: '', acquirer: '' };
      });

    // If env variable is true then validate that no fields are missing from all transactions
    // else throw error
    if (DE_SUBS_FINANCE_SUPPORT === 'true') {
      validateAllSubsTransactionsForMissingFields(
        mergedTrialTransaction,
        mergedNonTrialTransactions,
      );
    }
    // Add trial transactions as they are (without modifying them)
    const mergedTransactions = [
      ...mergedNonTrialTransactions,
      ...mergedTrialTransaction,
    ];

    // Return if no transactions found
    if (mergedTransactions.length === 0) {
      logger.info('No transactions found');
      return {
        statusCode: 200,
        body: JSON.stringify('No transactions found'),
      };
    }

    // Upload the CSV to S3
    const putResponse = await uploadCSV(mergedTransactions, startDate, endDate);

    logger.info(`putResponse: ${putResponse}`);
    return {
      statusCode: 200,
      body: JSON.stringify('Success'),
    };
  } catch (error: unknown) {
    logger.error('dailySubsFinanceData failure');
    logger.error(error);
    if (error instanceof Error) {
      throw new Error(error.message);
    } else {
      throw new Error(
        `dailySubsFinanceData error for event ${JSON.stringify(event)}`,
      );
    }
  }
};
