import { Readable } from 'node:stream';
import { gzip } from 'node:zlib';
import { MergedSubscriptionRecord } from '../../models/SubsTransaction';
import { put } from '../../services/s3/s3';
import {
  uploadCSV,
  createCSVStream,
  extractYearMonth,
  extractYearMonthDay,
} from './csv';
import { object1Merged } from '../../mocks/mergedTrasactions.mock';
import { stringify } from 'csv-stringify';

jest.mock('node:zlib', () => ({
  gzip: jest.fn(),
}));

jest.mock('node:util', () => ({
  promisify: jest.fn((fn: any) => fn),
}));

jest.mock('../../env/env', () => ({
  DE_SUBS_FINANCE_SUPPORT: 'true',
  DATA_HUB_BUCKET: 'test-bucket',
}));

jest.mock('csv-stringify');

jest.mock('../../services/s3/s3', () => ({
  put: jest.fn(),
}));

const mockGzip = gzip as unknown as jest.Mock;
const mockPut = put as jest.Mock;
const mockStringify = stringify as jest.Mock;

describe('CSV Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockStringify.mockImplementation(() => {
      const readable = new Readable();
      readable._read = () => {};
      process.nextTick(() => {
        readable.push('csv-data');
        readable.push(null);
      });

      const writable = {
        write: jest.fn(),
        end: jest.fn().mockImplementation(() => readable.emit('end')),
        on: jest.fn((event, callback) => {
          if (event === 'finish') process.nextTick(callback);
        }),
        [Symbol.asyncIterator]: () => readable[Symbol.asyncIterator](),
      };

      return writable;
    });
  });

  describe('extractYearMonth', () => {
    it('should extract year and month from a date string', () => {
      const dateString = '2024-06-15T00:00:00Z';
      const result = extractYearMonth(dateString);
      expect(result).toBe('2024-06');
    });

    it('should throw an error for an invalid date string', () => {
      expect(() => extractYearMonth('invalid-date')).toThrow(
        'Invalid date string',
      );
    });
  });

  describe('extractYearMonthDay', () => {
    it('should extract year, month, and day from a date string', () => {
      const dateString = '2024-06-15T00:00:00Z';
      const result = extractYearMonthDay(dateString);
      expect(result).toBe('2024-06-15');
    });

    it('should throw an error for an invalid date string', () => {
      expect(() => extractYearMonthDay('invalid-date')).toThrow(
        'Invalid date string',
      );
    });
  });

  describe('createCSVStream', () => {
    it('should create a gzipped CSV stream', async () => {
      const data: MergedSubscriptionRecord[] = [object1Merged];

      const csvData = Buffer.from('csv-data');
      mockGzip.mockResolvedValue(csvData);

      const result = await createCSVStream(data);
      expect(result).toBeInstanceOf(Object);
      expect(result.stream).toBeInstanceOf(Readable);

      const chunks: Buffer[] = [];
      for await (const chunk of result.stream) {
        chunks.push(chunk);
      }
      expect(Buffer.concat(chunks)).toEqual(csvData);
    });
  });

  describe('uploadCSV', () => {
    it('should upload a gzipped CSV to S3 with correct path partitioning', async () => {
      const startDate = new Date('2024-06-14T00:00:00Z');
      const endDate = new Date('2024-06-15T00:00:00Z');
      const data: MergedSubscriptionRecord[] = [object1Merged];

      mockGzip.mockResolvedValue(Buffer.from('gzipped-csv-data'));

      await uploadCSV(data, startDate, endDate);

      // Path uses endDate, filename uses startDate
      expect(mockPut).toHaveBeenCalledWith({
        bucketName: 'test-bucket',
        bodyObj: {
          length: expect.any(Number),
          stream: expect.any(Readable),
        },
        key: 'apollo/emsp/daily_paid_subscriptions/2024/06/15/SUBS_INV_20240614.csv.gz',
      });
    });

    it('should handle daily run pattern correctly', async () => {
      // Simulating a mocked daily lambda run for July 9th
      // (so end date is beggining of July 10th)
      const startDate = new Date('2025-07-09T00:00:00Z');
      const endDate = new Date('2025-07-10T00:00:00Z');
      const data: MergedSubscriptionRecord[] = [object1Merged];

      mockGzip.mockResolvedValue(Buffer.from('gzipped-csv-data'));

      await uploadCSV(data, startDate, endDate);

      expect(mockPut).toHaveBeenCalledWith({
        bucketName: 'test-bucket',
        bodyObj: {
          length: expect.any(Number),
          stream: expect.any(Readable),
        },
        key: 'apollo/emsp/daily_paid_subscriptions/2025/07/10/SUBS_INV_20250709.csv.gz',
      });
    });

    it('should handle re-generation pattern correctly', async () => {
      // Simulating re-generation of missing files: processing March 15th data
      const startDate = new Date('2025-03-15T00:00:00Z');
      const endDate = new Date('2025-03-16T00:00:00Z');
      const data: MergedSubscriptionRecord[] = [object1Merged];

      mockGzip.mockResolvedValue(Buffer.from('gzipped-csv-data'));

      await uploadCSV(data, startDate, endDate);

      expect(mockPut).toHaveBeenCalledWith({
        bucketName: 'test-bucket',
        bodyObj: {
          length: expect.any(Number),
          stream: expect.any(Readable),
        },
        key: 'apollo/emsp/daily_paid_subscriptions/2025/03/16/SUBS_INV_20250315.csv.gz',
      });
    });

    it('should handle single digit days with zero padding', async () => {
      const startDate = new Date('2024-06-01T00:00:00Z');
      const endDate = new Date('2024-06-02T00:00:00Z');
      const data: MergedSubscriptionRecord[] = [object1Merged];

      mockGzip.mockResolvedValue(Buffer.from('gzipped-csv-data'));

      await uploadCSV(data, startDate, endDate);

      expect(mockPut).toHaveBeenCalledWith({
        bucketName: 'test-bucket',
        bodyObj: {
          length: expect.any(Number),
          stream: expect.any(Readable),
        },
        key: 'apollo/emsp/daily_paid_subscriptions/2024/06/02/SUBS_INV_20240601.csv.gz',
      });
    });

    it('should throw an error if upload fails', async () => {
      const startDate = new Date('2024-06-14T00:00:00Z');
      const endDate = new Date('2024-06-15T00:00:00Z');
      const data: MergedSubscriptionRecord[] = [object1Merged];

      mockPut.mockRejectedValueOnce(new Error('S3 upload failed'));

      await expect(uploadCSV(data, startDate, endDate)).rejects.toThrow(
        'S3 upload failed',
      );
    });
  });
});
