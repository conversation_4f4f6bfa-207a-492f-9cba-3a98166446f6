import { handler } from '../src/index';
import { TagDeactivationService } from '../src/services/deactivation-service';
import { logger } from '../src/utils/logger';

jest.mock('../src/env', () => ({
    env: require('./mocks/env.mock').env,
}));
jest.mock('../src/services/deactivation-service');
jest.mock('../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

describe('deactivateDcsHtbTags lambda handler', () => {
    let mockTagDeactivationService: jest.MockedClass<
        typeof TagDeactivationService
    >;
    let mockProcessDeactivationEvent: jest.Mock;

    beforeEach(() => {
        jest.clearAllMocks();
        mockProcessDeactivationEvent = jest.fn();
        mockTagDeactivationService = TagDeactivationService as jest.MockedClass<
            typeof TagDeactivationService
        >;
        mockTagDeactivationService.prototype.processDeactivationEvent =
            mockProcessDeactivationEvent;
    });

    describe('single event processing', () => {
        const singleEvent = {
            userId: 'user123',
            tagIds: [
                {
                    tagId: '12345',
                    tagNotes: 'virtual-DCS',
                    tagCardNumber: 'card123',
                    tagStatus: 'ACTIVE',
                },
            ],
            country: 'NL',
        };

        it('should process a single event correctly', async () => {
            const expectedResult = {
                success: true,
                successfullyTerminated: [
                    {
                        tag: {
                            tagId: '12345',
                            tagNotes: 'virtual-DCS',
                            tagCardNumber: 'card123',
                            tagStatus: 'ACTIVE',
                        },
                        response: { status: 200 },
                    },
                ],
                failedToTerminate: [],
                balance: 100,
            };
            mockProcessDeactivationEvent.mockResolvedValue(expectedResult);
            await handler(singleEvent);
            expect(mockProcessDeactivationEvent).toHaveBeenCalledWith(
                singleEvent,
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining('Processing single deactivation event'),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining(
                    'Completed processing with status: success',
                ),
            );
            // CSV log assertion
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining('CSV_RESULT'),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining(
                    'user123,virtual-DCS,12345,NL,success,card123,ACTIVE',
                ),
            );
        });

        it('should log failure status when processing fails', async () => {
            const failureResult = {
                success: false,
                successfullyTerminated: [],
                failedToTerminate: [
                    { tag: { tagId: '12345' }, error: 'Failed' },
                ],
                balance: 100,
            };
            mockProcessDeactivationEvent.mockResolvedValue(failureResult);
            await handler(singleEvent);
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining(
                    'Completed processing with status: failed',
                ),
            );
        });
    });

    describe('batch event processing', () => {
        const batchEvents = [
            {
                userId: 'user123',
                tagIds: [
                    {
                        tagId: '12345',
                        tagNotes: 'virtual-DCS',
                        tagCardNumber: 'card123',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: 'NL',
            },
            {
                userId: 'user456',
                tagIds: [
                    {
                        tagId: '67890',
                        tagNotes: 'virtual-HTB',
                        tagCardNumber: 'card456',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: 'NL',
            },
        ];

        it('should process batch events correctly', async () => {
            mockProcessDeactivationEvent
                .mockResolvedValueOnce({
                    success: true,
                    successfullyTerminated: [
                        {
                            tag: {
                                tagId: '12345',
                                tagNotes: 'virtual-DCS',
                                tagCardNumber: 'card123',
                                tagStatus: 'ACTIVE',
                            },
                            response: { status: 200 },
                        },
                    ],
                    failedToTerminate: [],
                    balance: 100,
                })
                .mockResolvedValueOnce({
                    success: true,
                    successfullyTerminated: [
                        {
                            tag: {
                                tagId: '67890',
                                tagNotes: 'virtual-HTB',
                                tagCardNumber: 'card456',
                                tagStatus: 'ACTIVE',
                            },
                            response: { status: 200 },
                        },
                    ],
                    failedToTerminate: [],
                    balance: 200,
                });
            await handler(batchEvents);
            expect(mockProcessDeactivationEvent).toHaveBeenCalledTimes(2);
            expect(logger.info).toHaveBeenCalledWith(
                'Processing batch of 2 deactivation events',
            );
            expect(logger.info).toHaveBeenCalledWith(
                'Completed processing batch of 2 deactivation events',
            );
            // CSV log assertion for both users
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining('CSV_RESULT'),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining(
                    'user123,virtual-DCS,12345,NL,success,card123,ACTIVE',
                ),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining(
                    'user456,virtual-HTB,67890,NL,success,card456,ACTIVE',
                ),
            );
        });

        it('should handle errors in batch processing', async () => {
            mockProcessDeactivationEvent
                .mockResolvedValueOnce({
                    success: true,
                    successfullyTerminated: [
                        { tag: { tagId: '12345' }, response: { status: 200 } },
                    ],
                    failedToTerminate: [],
                    balance: 100,
                })
                .mockRejectedValueOnce(new Error('Processing failed'));
            await handler(batchEvents);
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Failed to process event 2:'),
            );
        });

        it('should count successful and failed events correctly', async () => {
            mockProcessDeactivationEvent
                .mockResolvedValueOnce({
                    success: true,
                    successfullyTerminated: [
                        { tag: { tagId: '12345' }, response: { status: 200 } },
                    ],
                    failedToTerminate: [],
                    balance: 100,
                })
                .mockResolvedValueOnce({
                    success: false,
                    successfullyTerminated: [],
                    failedToTerminate: [
                        { tag: { tagId: '67890' }, error: 'Failed' },
                    ],
                    balance: 200,
                });
            await handler(batchEvents);
            // Only check logger calls, not returned value
            expect(logger.info).toHaveBeenCalledWith(
                'Completed processing batch of 2 deactivation events',
            );
        });
    });

    describe('input validation integration', () => {
        it('should handle input validation errors gracefully for single event', async () => {
            const invalidEvent = {
                userId: '',
                tagIds: [
                    {
                        tagId: '',
                        tagNotes: '',
                        tagCardNumber: '',
                        tagStatus: '',
                    },
                ],
            };
            await handler(invalidEvent);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should process clean input successfully', async () => {
            const cleanEvent = {
                userId: 'user123',
                tagIds: [
                    {
                        tagId: 'tag1',
                        tagNotes: 'clean note',
                        tagCardNumber: 'card123',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: 'NL',
            };

            const expectedResult = {
                success: true,
                successfullyTerminated: [],
                failedToTerminate: [],
                balance: 100,
            };
            mockProcessDeactivationEvent.mockResolvedValue(expectedResult);
            await handler(cleanEvent);
            expect(mockProcessDeactivationEvent).toHaveBeenCalledWith(
                cleanEvent,
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining('Processing single deactivation event'),
            );
        });

        it('should handle input validation errors gracefully for single event with both userId and tagIds missing', async () => {
            const invalidEvent = {
                userId: '',
                tagIds: [],
            };
            await handler(invalidEvent);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should process event with only userId present', async () => {
            const eventWithOnlyUserId = {
                userId: 'userOnly',
                tagIds: [], // required by type
                country: 'NL',
            };
            const expectedResult = {
                success: true,
                successfullyTerminated: [],
                failedToTerminate: [],
                balance: 100,
            };
            mockProcessDeactivationEvent.mockResolvedValue(expectedResult);
            await handler(eventWithOnlyUserId);
            expect(mockProcessDeactivationEvent).toHaveBeenCalledWith(
                expect.objectContaining({ userId: 'userOnly' }),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.stringContaining('Processing single deactivation event'),
            );
        });

        it('should process event with only tagIds present', async () => {
            const eventWithOnlyTagIds = {
                userId: '', // required by type
                tagIds: [
                    {
                        tagId: 'tagOnly',
                        tagNotes: 'virtual-DCS',
                        tagCardNumber: 'cardOnly',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: 'NL',
            };
            await handler(eventWithOnlyTagIds);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should handle input validation errors gracefully for batch events', async () => {
            const invalidBatchEvents = [
                {
                    userId: '',
                    tagIds: [
                        {
                            tagId: '',
                            tagNotes: '',
                            tagCardNumber: '',
                            tagStatus: '',
                        },
                    ],
                },
            ];
            await handler(invalidBatchEvents);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should throw if country is missing when userId is present', async () => {
            const eventMissingCountry = {
                userId: 'userWithNoCountry',
                tagIds: [
                    {
                        tagId: 'tag1',
                        tagNotes: 'virtual-HTB',
                        tagCardNumber: 'card1',
                        tagStatus: 'ACTIVE',
                    },
                ],
                // country missing
            };
            await handler(eventMissingCountry);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should throw if country is empty when userId is present', async () => {
            const eventEmptyCountry = {
                userId: 'userWithEmptyCountry',
                tagIds: [
                    {
                        tagId: 'tag1',
                        tagNotes: 'virtual-HTB',
                        tagCardNumber: 'card1',
                        tagStatus: 'ACTIVE',
                    },
                ],
                country: '',
            };
            await handler(eventEmptyCountry);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });

        it('should process event with only tagIds and no country', async () => {
            const eventWithOnlyTagIds = {
                userId: '', // required by type
                tagIds: [
                    {
                        tagId: 'tagOnly',
                        tagNotes: 'virtual-DCS',
                        tagCardNumber: 'cardOnly',
                        tagStatus: 'ACTIVE',
                    },
                ],
            };
            await handler(eventWithOnlyTagIds);
            expect(mockProcessDeactivationEvent).not.toHaveBeenCalled();
            expect(logger.error).toHaveBeenCalledWith(
                expect.stringContaining('Input validation failed'),
            );
        });
    });
});
