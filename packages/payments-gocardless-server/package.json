{"name": "@bp/feature-payments-gocardless-server", "version": "0.0.1", "private": true, "dependencies": {"@apollo/federation": "^0.33.3", "apollo-opentracing": "^2.1.55", "apollo-server": "^3.10.2", "apollo-server-core": "^3.10.2", "apollo-server-express": "^3.10.2", "axios": "^1.10.0", "body-parser": "^1.19.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^10.0.0", "eslint-plugin-prettier": "^4.0.0", "express": "^4.17.1", "gocardless-nodejs": "^3.14.0", "graphql": "^15.5.3", "helmet": "^4.6.0", "moment": "^2.29.1", "opentracing": "^0.14.5", "schemaglue": "^4.3.0", "winston": "^3.8.2"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/runtime": "^7.15.4", "@bp/eslint-plugin": "^0.0.3", "@types/compression": "^1.7.2", "@types/jaeger-client": "^3.18.3", "@types/jest": "^27.5.2", "babel-jest": "^29.7.0", "eslint": "^8.20.0", "jest": "^29.7.0", "nodemon": "^2.0.20", "prettier": "2.7.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}