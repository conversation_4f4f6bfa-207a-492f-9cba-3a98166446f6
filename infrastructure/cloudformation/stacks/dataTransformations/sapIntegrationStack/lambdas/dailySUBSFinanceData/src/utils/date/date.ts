export const getYesterdayStart = (): Date => {
  const now = new Date();
  now.setUTCHours(0, 0, 0, 0);

  const yesterdayStart = new Date(now);
  yesterdayStart.setUTCDate(now.getUTCDate() - 1);

  return yesterdayStart;
};

export const getTodayStart = (): Date => {
  const today = new Date();
  today.setUTCHours(0, 0, 0, 0);
  return today;
};

export const getTodaysDay = (): string => {
  const now = new Date();
  const day = now.getDate();
  return day < 10 ? `0${day}` : day.toString();
};
