export interface DeactivateTagEvent {
    userId: string;
    country?: string;
    tagIds: Array<Tag>;
}
export interface Tag {
    tagId: string;
    tagCardNumber: string;
    tagNotes: string;
    tagStatus: string;
}
export interface AccessTokenResponse {
    success: boolean;
    token?: string;
}

export interface GetHTBTokenResponse {
    data: {
        initSession: {
            sessionToken: string;
            refreshToken: string;
        };
    };
}

export interface GetUserInfoResponse {
    userInfo: {
        type: string;
        country: string;
        balance: number;
        tagIds: Array<Tag>;
    };
}

export interface DeactivationResult {
    success: boolean;
    successfullyTerminated: Array<{ tag: Tag; response: any }>;
    failedToTerminate: Array<{ tag: Tag; response?: any; error?: any }>;
    balance: number;
    message?: string;
}
