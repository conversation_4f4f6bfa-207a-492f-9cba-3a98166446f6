import { Readable } from 'node:stream';
import { put as s3Put } from '../../clients/s3';
import { PROVIDER } from '../../common/enums';
import { constructFileKey } from '../constructFileKey/constructFileKey';

const {
  DATA_HUB_BUCKET = 'ws-01t0-shared-ingestion-bucket',
  DCS_OUTBOUND_ROAMING_BUCKET = 'ws-00f0-dcs-outbound-roaming',
} = process.env;

export async function writeToS3(
  provider: PROVIDER,
  csvData: Buffer,
  gzData: { stream: Readable; length: number },
  startDate: string,
  endDate: string,
) {
  try {
    const { dataHubKey, emspKey } = constructFileKey(
      provider,
      startDate,
      endDate,
    );
    // Write to eMSP bucket
    try {
      await s3Put(DCS_OUTBOUND_ROAMING_BUCKET, emspKey, csvData);
    } catch (error) {
      console.log('ERROR putting in s3', error);
    }
    // Write to Data Hub bucket
    if (DATA_HUB_BUCKET) {
      await s3Put(
        DATA_HUB_BUCKET,
        dataHubKey,
        gzData.stream,
        'bucket-owner-full-control',
        gzData.length,
      );
    }
    console.info(`File written successfully to S3 for: ${provider}`);
  } catch (error: unknown) {
    throw new Error(
      `Error writing file to S3 for ${provider} with error: ${error}`,
    );
  }
}
