{"name": "ev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "handle sqs queue events from evc", "license": "UNLICENSED", "main": "dist/src/index.js", "dependencies": {"@aws-sdk/client-sqs": "^3.131.0", "axios": "1.10.0", "dotenv": "^16.0.1", "graphql": "^16.5.0", "graphql-request": "^4.3.0", "ioredis": "^5.1.0", "winston": "^3.8.1"}, "devDependencies": {"@babel/preset-typescript": "^7.18.6", "@types/aws-lambda": "^8.10.101", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "babel-jest": "^27.5.1", "ioredis-mock": "^8.2.2", "jest": "^27.5.1", "ts-node": "^10.8.1", "typescript": "^4.7.4"}}