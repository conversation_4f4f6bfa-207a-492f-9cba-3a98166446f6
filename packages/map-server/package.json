{"name": "@bp/feature-map-server", "version": "1.0.0", "private": true, "main": "./dist/index.js", "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf dist/", "dev": "nodemon --config nodemon.json src/index.ts", "postinstall": "rm -rf ./node_modules/hpagent/test", "lint": "eslint . --ext .ts", "lint:fix": "npm run lint -- --fix", "start": "node ./dist/index.js", "stop": "npx kill-port 4003", "test": "jest", "test:ci": "jest --ci", "types:check": "tsc --noEmit", "types:generate": "graphql-codegen --config codegen.yml && npm run types:stage", "types:stage": "git add src/types/graphql.ts"}, "dependencies": {"@apollo/subgraph": "^2.7.8", "@aws-sdk/client-s3": "^3.525.0", "@elastic/elasticsearch": "7.13", "apollo-server": "^3.10.2", "apollo-server-core": "^3.10.2", "apollo-server-errors": "^3.3.1", "apollo-server-express": "^3.10.2", "axios": "^1.10.0", "dataloader": "^2.0.0", "dotenv": "^8.2.0", "express": "^4.17.1", "graphql": "^16.6.0", "graphql-tag": "^2.11.0", "ioredis": "^4.16.2", "morgan": "^1.10.0", "ngeohash": "^0.6.3", "schemaglue": "^4.0.4", "supercluster": "^7.1.4", "winston": "^3.8.2"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@bp/eslint-plugin": "^0.0.3", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "4.1.6", "@types/compression": "^1.7.2", "@types/ioredis": "^4.28.10", "@types/ioredis-mock": "^5.6.0", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.3", "@types/ngeohash": "^0.6.4", "@types/node": "^17.0.31", "@types/supercluster": "^7.1.0", "babel-jest": "^29.5.0", "eslint": "^8.20.0", "ioredis-mock": "^7.2.0", "jest": "^29.5.0", "jest-fetch-mock": "^3.0.3", "nodemon": "^3.1.10", "prettier": "2.7.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}