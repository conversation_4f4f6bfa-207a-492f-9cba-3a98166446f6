import {
    validateString,
    validateTag,
    validateDeactivateTagEvent,
} from '../src/utils/input-validation';
import { DeactivateTagEvent, Tag } from '../src/types/types';

describe('Input Validation', () => {
    describe('validateString', () => {
        it('should return the input string as is', () => {
            const input = '<script>alert("XSS")</script>Hello';
            const result = validateString(input);
            expect(result).toBe(input);
        });
        it('should handle empty strings', () => {
            const result = validateString('');
            expect(result).toBe('');
        });
        it('should warn and return empty string for non-string input', () => {
            const result = validateString(123 as any);
            expect(result).toBe('');
        });
    });
    describe('validateTag', () => {
        it('should return true if tag is valid', () => {
            const validTag: Tag = {
                tagId: 'tag123',
                tagCardNumber: 'card456',
                tagNotes: 'Valid note',
                tagStatus: 'ACTIVE',
            };
            const result = validateTag(validTag);
            expect(result).toBe(true);
        });
        it('should return false for invalid tag object', () => {
            expect(validateTag(null as any)).toBe(false);
            expect(validateTag('invalid' as any)).toBe(false);
        });
        it('should return false for missing required fields', () => {
            const incompleteTag = {
                tagId: '',
                tagCardNumber: '',
                tagNotes: '',
                tagStatus: '',
            };
            expect(validateTag(incompleteTag as Tag)).toBe(false);
        });
    });
    describe('validateDeactivateTagEvent', () => {
        it('should return the event as is if valid', () => {
            const validEvent: DeactivateTagEvent = {
                userId: 'user123',
                country: 'NL',
                tagIds: [
                    {
                        tagId: 'tag1',
                        tagCardNumber: 'card1',
                        tagNotes: 'note1',
                        tagStatus: 'ACTIVE',
                    },
                ],
            };
            const result = validateDeactivateTagEvent(validEvent);
            expect(result).toEqual(validEvent);
        });
        it('should throw error for missing required fields', () => {
            const invalidEvent = {
                userId: '',
                tagIds: [],
                country: '',
            } as DeactivateTagEvent;
            expect(() => validateDeactivateTagEvent(invalidEvent)).toThrow();
        });

        it('should throw error for missing country field', () => {
            const invalidEvent = {
                userId: 'user123',
                tagIds: [],
            } as DeactivateTagEvent;
            expect(() => validateDeactivateTagEvent(invalidEvent)).toThrow(
                'country is required when userId is provided',
            );
        });
        it('should throw error for invalid event object', () => {
            expect(() => validateDeactivateTagEvent(null as any)).toThrow(
                'Invalid deactivation event provided',
            );
        });
        it('should filter out incomplete tags and throw if all are invalid', () => {
            const incompleteTagEvent = {
                userId: 'user123',
                tagIds: [
                    {
                        tagId: '',
                        tagNotes: '',
                        tagCardNumber: '',
                        tagStatus: '',
                    },
                ],
                country: 'NL',
            };
            expect(() =>
                validateDeactivateTagEvent(
                    incompleteTagEvent as DeactivateTagEvent,
                ),
            ).toThrow('All provided tags are missing required fields');
        });
        it('should filter out only invalid tags and keep valid ones', () => {
            const event: DeactivateTagEvent = {
                userId: 'user123',
                country: 'NL',
                tagIds: [
                    {
                        tagId: 'tag1',
                        tagCardNumber: 'card1',
                        tagNotes: 'note1',
                        tagStatus: 'ACTIVE',
                    },
                    {
                        tagId: '',
                        tagCardNumber: '',
                        tagNotes: '',
                        tagStatus: '',
                    },
                ],
            };
            const result = validateDeactivateTagEvent(event);
            expect(result.tagIds.length).toBe(1);
            expect(result.tagIds[0].tagId).toBe('tag1');
        });
    });
});
