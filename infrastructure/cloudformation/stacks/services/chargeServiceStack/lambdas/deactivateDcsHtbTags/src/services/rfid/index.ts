import { request } from 'graphql-request';
import { BLOCK_RFID } from './queries';
import { env } from './../../env';
import { BlockRFIDInput, QueryDefaultResponse } from '../../utils/constants';
import { logger } from '../../utils/logger';

// Internal service config required to make calls to our micro-services, the secret should match the value set
// in the private Apollo Gateway servers environment variables
export const blockRFID = async ({
    userId,
    country,
    reasonForBlocking,
    cardUid,
    cardNumber,
}: BlockRFIDInput) =>
    request(
        env.PRIVATE_GATEWAY_SERVER_HTTP,
        BLOCK_RFID,
        {
            userId,
            country,
            reasonForBlocking,
            cardUid,
            cardNumber,
        },
        { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
    )
        .then(
            (queryResponse: { blockRFID: QueryDefaultResponse }) =>
                queryResponse.blockRFID,
        )
        .catch((err) => {
            logger.error(
                `Could not block RFID for user ${userId}: ${err.message}`,
            );
            throw new Error(`message: ${err.message}`);
        });
