import {
    BaseDeactivationCommand,
    DcsDeactivationCommand,
    HtbDeactivationCommand,
    RfidDeactivationCommand,
} from '../src/commands/deactivation-commands';
import { Tag } from '../src/types/types';
import { dcsTerminateContract } from '../src/utils/dcs';
import { deactivateHTBCard } from '../src/utils/htb';
import { blockRFID } from '../src/services/rfid';
import { updateTagInternal } from '../src/services/user';
import { logger } from '../src/utils/logger';

jest.mock('../src/env', () => ({
    env: require('./mocks/env.mock').env,
}));
jest.mock('../src/utils/dcs');
jest.mock('../src/utils/htb');
jest.mock('../src/services/rfid');
jest.mock('../src/services/user');
jest.mock('../src/utils/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
    },
}));

describe('Deactivation Commands', () => {
    const mockTag: Tag = {
        tagId: '12345',
        tagNotes: 'virtual-DCS',
        tagCardNumber: 'CARD123',
        tagStatus: 'ACTIVE',
    };

    const userId = 'user-123';
    const country = 'NL';

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('BaseDeactivationCommand', () => {
        class TestCommand extends BaseDeactivationCommand {
            protected async deactivateSpecificTagType() {
                return { status: 200, message: 'Success' };
            }

            protected async updateTagStatus() {}
        }

        it('should validate the tag before deactivation', async () => {
            const invalidTag: Tag = {
                tagId: '',
                tagNotes: '',
                tagCardNumber: '',
                tagStatus: '',
            };

            const command = new TestCommand(invalidTag, userId, country);
            const result = await command.execute();

            expect(result.status).toBe(400);
            expect(result.message).toContain(
                "Tag cannot be updated because it's missing key data",
            );
        });

        it('should call updateTagStatus after successful deactivation', async () => {
            const command = new TestCommand(mockTag, userId, country);
            const updateSpy = jest.spyOn(command as any, 'updateTagStatus');

            await command.execute();

            expect(updateSpy).toHaveBeenCalled();
        });

        it('should catch errors during execution and return a 500 status', async () => {
            const command = new TestCommand(mockTag, userId, country);
            jest.spyOn(
                command as any,
                'deactivateSpecificTagType',
            ).mockRejectedValue(new Error('Test error'));

            const result = await command.execute();

            expect(result.status).toBe(500);
            expect(result.message).toContain('Error deactivating tag');
            expect(logger.error).toHaveBeenCalled();
        });
    });

    describe('DcsDeactivationCommand', () => {
        it('should call dcsTerminateContract with correct parameters', async () => {
            const mockResponse = { status: 200, message: 'Success' };
            (dcsTerminateContract as jest.Mock).mockResolvedValue(mockResponse);

            const command = new DcsDeactivationCommand(
                mockTag,
                userId,
                country,
            );
            const result = await command.execute();

            expect(dcsTerminateContract).toHaveBeenCalledWith({
                dcsContractId: mockTag.tagId,
                country,
            });
            expect(result).toBe(mockResponse);
        });

        it('should call updateTagInternal after successful deactivation', async () => {
            const mockResponse = { status: 200, message: 'Success' };
            (dcsTerminateContract as jest.Mock).mockResolvedValue(mockResponse);

            const command = new DcsDeactivationCommand(
                mockTag,
                userId,
                country,
            );
            await command.execute();

            expect(updateTagInternal).toHaveBeenCalledWith({
                salesforceId: userId,
                country,
                tagCardNumber: mockTag.tagCardNumber,
                tagStatus: 'TERMINATED',
                tagBarredDatetime: expect.any(String),
            });
        });
    });

    describe('HtbDeactivationCommand', () => {
        it('should call deactivateHTBCard with correct parameters', async () => {
            const mockResponse = { status: 200, message: 'Success' };
            (deactivateHTBCard as jest.Mock).mockResolvedValue(mockResponse);

            const command = new HtbDeactivationCommand(
                mockTag,
                userId,
                country,
            );
            const result = await command.execute();

            expect(deactivateHTBCard).toHaveBeenCalledWith({
                serialNumber: mockTag.tagId,
                cardNumber: mockTag.tagId,
                country,
            });
            expect(result).toBe(mockResponse);
        });

        it('should call updateTagInternal after successful deactivation', async () => {
            const mockResponse = { status: 200, message: 'Success' };
            (deactivateHTBCard as jest.Mock).mockResolvedValue(mockResponse);

            const command = new HtbDeactivationCommand(
                mockTag,
                userId,
                country,
            );
            await command.execute();

            expect(updateTagInternal).toHaveBeenCalledWith({
                salesforceId: userId,
                country,
                tagCardNumber: mockTag.tagCardNumber,
                tagStatus: 'TERMINATED',
                tagBarredDatetime: expect.any(String),
            });
        });
    });

    describe('RfidDeactivationCommand', () => {
        const rfidTag: Tag = {
            tagId: '12345',
            tagNotes: 'physical-RFID',
            tagCardNumber: 'CARD123',
            tagStatus: 'ACTIVE',
        };

        it('should require userId for RFID deactivation', async () => {
            const command = new RfidDeactivationCommand(
                rfidTag,
                undefined,
                country,
            );
            const result = await command.execute();

            expect(result.status).toBe(400);
            expect(result.message).toContain(
                'Cannot block RFID as userId is not defined',
            );
            expect(blockRFID).not.toHaveBeenCalled();
        });

        it('should call blockRFID with correct parameters when userId is provided', async () => {
            const mockResponse = { status: 200, message: 'Success' };
            (blockRFID as jest.Mock).mockResolvedValue(mockResponse);

            const command = new RfidDeactivationCommand(
                rfidTag,
                userId,
                country,
            );
            const result = await command.execute();

            expect(blockRFID).toHaveBeenCalledWith({
                userId,
                country,
                reasonForBlocking: 'Request to Cancel',
                cardUid: rfidTag.tagId,
                cardNumber: rfidTag.tagCardNumber,
            });
            expect(result).toBe(mockResponse);
        });

        it('should catch errors during RFID deactivation', async () => {
            (blockRFID as jest.Mock).mockRejectedValue(
                new Error('RFID blocking error'),
            );

            const command = new RfidDeactivationCommand(
                rfidTag,
                userId,
                country,
            );
            const result = await command.execute();

            expect(result.status).toBe(500);
            expect(result.message).toContain('Error deactivating tag');
            expect(logger.error).toHaveBeenCalled();
        });
    });
});
