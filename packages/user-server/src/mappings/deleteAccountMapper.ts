import env from '../env';
import { updateUserAurora } from '../providers/aurora/auroraServices';
import { DeleteAccountConfig, UpdateUserInternalInput } from '../types/account';
import { logger } from '../utils/logger';

export const deleteAccountConfig: DeleteAccountConfig = {
  US: {
    cancelSubscriptionEnabled: true,
    customerSupportEmail: '',
    deletionEmail: env.DATA_PRIVACY_EMAIL || '',
    deleteEmailTitle: 'Account Deletion Requested',
    deletionEmailTemplate: 'bppulse-account-deletion-email-to-dataprivacy-v1',
    customerEmailTemplate: 'rtbf-account-deletion-email-to-customer-uk-latest',
    refundEmail: '',
    refundEmailTemplate: 'bppulse-account-deletion-email-to-refund-team-v1',
  },
  UK: {
    cancelSubscriptionEnabled: true,
    customerSupportEmail: env.UK_CUSTOMER_SUPPORT_EMAIL || '',
    deletionEmail:
      env.ENABLE_DELETION_ACCOUNT_CONFIG_VALUE === 'false'
        ? env.UK_DATA_PRIVACY_EMAIL || ''
        : env.DATA_PRIVACY_EMAIL || '',
    deleteEmailTitle: 'Account Deletion Requested',
    deletionEmailTemplate: 'bppulse-account-deletion-email-to-dataprivacy-v1',
    customerEmailTemplate: 'rtbf-account-deletion-email-to-customer-uk-latest',
    refundEmail: env.UK_REFUND_EMAIL || '',
    refundEmailTemplate: 'bppulse-account-deletion-email-to-refund-team-v1',
  },
  NL: {
    cancelSubscriptionEnabled: false,
    customerSupportEmail: env.NL_DATA_PRIVACY_EMAIL || '',
    deletionEmail:
      env.ENABLE_DELETION_ACCOUNT_CONFIG_VALUE === 'false'
        ? env.NL_DATA_PRIVACY_EMAIL || ''
        : env.DATA_PRIVACY_EMAIL || '',
    deleteEmailTitle: 'Verzoek tot accountverwijdering ingediend',
    deletionEmailTemplate: 'rtbf-delete-account-to-privacy-team-v1-nl',
    customerEmailTemplate: 'rtbf-account-deletion-email-to-customer-nl-v2',
  },
  DE: {
    cancelSubscriptionEnabled: true,
    customerSupportEmail: env.DE_CUSTOMER_SUPPORT_EMAIL || '',
    deletionEmail:
      env.ENABLE_DELETION_ACCOUNT_CONFIG_VALUE === 'false'
        ? env.DE_DATA_PRIVACY_EMAIL || ''
        : env.DATA_PRIVACY_EMAIL || '',
    deleteEmailTitle: 'Kontolöschung beantragt',
    deletionEmailTemplate: 'rtbf-delete-account-to-privacy-team-v1-de',
    customerEmailTemplate: 'rtbf-account-deletion-email-to-customer-de-v1',
  },
  ES: {
    cancelSubscriptionEnabled: false,
    deletionEmail:
      env.ENABLE_DELETION_ACCOUNT_CONFIG_VALUE === 'false'
        ? env.ES_DATA_PRIVACY_EMAIL || ''
        : env.DATA_PRIVACY_EMAIL || '',
    deleteEmailTitle: 'Eliminación de cuenta solicitada',
    deletionEmailTemplate: 'rtbf-delete-account-to-privacy-team-v1-es',
    customerEmailTemplate: 'rtbf-account-deletion-email-to-customer-es-v1',
  },
};

const deleteAurora = (
  {
    userId,
    userCancelledReason,
    country,
    userCancelledDateTime,
  }: UpdateUserInternalInput,
  logTraceId: string,
) =>
  updateUserAurora(
    { userId, userCancelledReason, country, userCancelledDateTime },
    logTraceId,
    logger,
  );

export const deleteAccountFunction = {
  NL: deleteAurora,
  DE: deleteAurora,
};
