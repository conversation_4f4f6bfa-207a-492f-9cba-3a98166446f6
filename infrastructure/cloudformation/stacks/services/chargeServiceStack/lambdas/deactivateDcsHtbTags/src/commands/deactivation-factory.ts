import { Tag } from '../types/types';
import { TAG_NOTES } from '../utils/constants';
import {
    DcsDeactivationCommand,
    DeactivationCommand,
    HtbDeactivationCommand,
    RfidDeactivationCommand,
} from './deactivation-commands';

export class DeactivationCommandFactory {
    static createCommand(
        tag: Tag,
        userId: string | undefined,
        country: string,
    ): DeactivationCommand {
        const tagType = tag.tagNotes.toUpperCase();
        switch (tagType) {
            case TAG_NOTES.DCS:
                return new DcsDeactivationCommand(tag, userId, country);
            case TAG_NOTES.HTB:
                return new HtbDeactivationCommand(tag, userId, country);
            case TAG_NOTES.RFID:
                return new RfidDeactivationCommand(tag, userId, country);
            default:
                throw new Error(`Unsupported tag type: ${tagType}`);
        }
    }
}
