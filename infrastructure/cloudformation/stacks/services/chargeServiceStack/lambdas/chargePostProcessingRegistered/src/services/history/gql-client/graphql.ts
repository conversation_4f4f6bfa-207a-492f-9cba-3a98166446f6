/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
};

export type DeleteUserChargeEventsResponse = {
  __typename?: 'DeleteUserChargeEventsResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type History_BlockingFee = {
  __typename?: 'History_BlockingFee';
  duration?: Maybe<Scalars['Float']['output']>;
  price?: Maybe<Scalars['Float']['output']>;
};

export type History_BlockingFeeInput = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ChargeDetails = {
  __typename?: 'History_ChargeDetails';
  authId?: Maybe<Scalars['String']['output']>;
  authType?: Maybe<Scalars['String']['output']>;
  chargeAdditionalFees?: Maybe<Scalars['String']['output']>;
  chargeAdditionalFeesGross?: Maybe<Scalars['String']['output']>;
  chargeBaseFee?: Maybe<Scalars['String']['output']>;
  chargeBaseFeeGross?: Maybe<Scalars['String']['output']>;
  chargeGross?: Maybe<Scalars['String']['output']>;
  chargeNet?: Maybe<Scalars['String']['output']>;
  chargeTax?: Maybe<Scalars['String']['output']>;
  chargeTaxPercentage?: Maybe<Scalars['String']['output']>;
  co2Saving?: Maybe<Scalars['Float']['output']>;
  creditCardPan?: Maybe<Scalars['String']['output']>;
  creditCardType?: Maybe<Scalars['String']['output']>;
  creditCardUuid?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  discount?: Maybe<Scalars['Float']['output']>;
  energyConsumed?: Maybe<History_ChargeMetaData>;
  grossAmountLocalCurrency?: Maybe<Scalars['String']['output']>;
  isStandardInvoice?: Maybe<Scalars['Boolean']['output']>;
  meterEnd?: Maybe<Scalars['String']['output']>;
  meterStart?: Maybe<Scalars['String']['output']>;
  minCost?: Maybe<Scalars['String']['output']>;
  offerCodes?: Maybe<Array<History_OfferCode>>;
  overstay?: Maybe<History_OverstayCost>;
  payback?: Maybe<Scalars['String']['output']>;
  power?: Maybe<History_ChargeMetaData>;
  providerChargeIds?: Maybe<Array<Maybe<History_ProviderChargeIds>>>;
  rateName?: Maybe<Scalars['String']['output']>;
  stateOfCharge?: Maybe<History_ChargeMetaData>;
  status?: Maybe<Scalars['String']['output']>;
  tagId?: Maybe<Scalars['String']['output']>;
  totalGross?: Maybe<Scalars['String']['output']>;
  unitCost?: Maybe<Scalars['String']['output']>;
  unitOfMeasure?: Maybe<Scalars['String']['output']>;
};

export type History_ChargeDetailsInput = {
  authId?: InputMaybe<Scalars['String']['input']>;
  authType?: InputMaybe<Scalars['String']['input']>;
  chargeAdditionalFees?: InputMaybe<Scalars['Float']['input']>;
  chargeBaseFee?: InputMaybe<Scalars['Float']['input']>;
  chargeGross?: InputMaybe<Scalars['Float']['input']>;
  chargeNet?: InputMaybe<Scalars['Float']['input']>;
  chargeTax?: InputMaybe<Scalars['Float']['input']>;
  chargeTaxPercentage?: InputMaybe<Scalars['Float']['input']>;
  co2Saving?: InputMaybe<Scalars['String']['input']>;
  creditCardPan?: InputMaybe<Scalars['String']['input']>;
  creditCardType?: InputMaybe<Scalars['String']['input']>;
  creditCardUuid?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  energyConsumed?: InputMaybe<History_ChargeMetaDataInput>;
  grossAmountLocalCurrency?: InputMaybe<Scalars['Float']['input']>;
  meterEnd?: InputMaybe<Scalars['String']['input']>;
  meterStart?: InputMaybe<Scalars['String']['input']>;
  minCost?: InputMaybe<Scalars['Float']['input']>;
  overstay?: InputMaybe<History_OverstayCostInput>;
  payback?: InputMaybe<Scalars['String']['input']>;
  power?: InputMaybe<History_ChargeMetaDataInput>;
  providerChargeIds?: InputMaybe<Array<InputMaybe<History_ChargeIdsInput>>>;
  rateName?: InputMaybe<Scalars['String']['input']>;
  stateOfCharge?: InputMaybe<History_ChargeMetaDataInput>;
  status?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  totalGross?: InputMaybe<Scalars['Float']['input']>;
  unitCost?: InputMaybe<Scalars['Float']['input']>;
  unitOfMeasure?: InputMaybe<Scalars['String']['input']>;
};

export type History_ChargeDuration = {
  __typename?: 'History_ChargeDuration';
  days?: Maybe<Scalars['Int']['output']>;
  formatted?: Maybe<Scalars['String']['output']>;
  hours?: Maybe<Scalars['Int']['output']>;
  minutes?: Maybe<Scalars['Int']['output']>;
  seconds?: Maybe<Scalars['Int']['output']>;
};

export type History_ChargeIdsInput = {
  type?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export type History_ChargeMetaData = {
  __typename?: 'History_ChargeMetaData';
  units?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['Float']['output']>;
};

export type History_ChargeMetaDataInput = {
  units?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ChargePoint = {
  __typename?: 'History_ChargePoint';
  address?: Maybe<Scalars['String']['output']>;
  apolloExternalId?: Maybe<Scalars['String']['output']>;
  apolloInternalId?: Maybe<Scalars['String']['output']>;
  blockingFee?: Maybe<History_BlockingFee>;
  chargepointId?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  connector?: Maybe<History_ConnectorHistory>;
  country?: Maybe<Scalars['String']['output']>;
  cpo?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  displayAddress?: Maybe<Scalars['String']['output']>;
  grossUnitCost?: Maybe<Scalars['Float']['output']>;
  postcode?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  providerExternalId?: Maybe<Scalars['String']['output']>;
  publicName?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type History_ChargepointInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  apolloExternalId?: InputMaybe<Scalars['String']['input']>;
  apolloInternalId?: InputMaybe<Scalars['String']['input']>;
  blockingFee?: InputMaybe<History_BlockingFeeInput>;
  city?: InputMaybe<Scalars['String']['input']>;
  connector?: InputMaybe<History_ConnectorInput>;
  country?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  displayAddress?: InputMaybe<Scalars['String']['input']>;
  grossUnitCost?: InputMaybe<Scalars['Float']['input']>;
  postcode?: InputMaybe<Scalars['String']['input']>;
  provider?: InputMaybe<Scalars['String']['input']>;
  providerExternalId?: InputMaybe<Scalars['String']['input']>;
  publicName?: InputMaybe<Scalars['String']['input']>;
  unit?: InputMaybe<Scalars['String']['input']>;
};

export type History_ConnectorHistory = {
  __typename?: 'History_ConnectorHistory';
  connectorExternalId?: Maybe<Scalars['String']['output']>;
  connectorInternalId?: Maybe<Scalars['String']['output']>;
  phase?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type History_ConnectorInput = {
  connectorExternalId?: InputMaybe<Scalars['String']['input']>;
  connectorInternalId?: InputMaybe<Scalars['String']['input']>;
  phase?: InputMaybe<Scalars['String']['input']>;
  rating?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type History_CreateHistoryResponse = {
  __typename?: 'History_CreateHistoryResponse';
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type History_HistoryRecord = {
  __typename?: 'History_HistoryRecord';
  cdrType?: Maybe<Scalars['String']['output']>;
  cdr_filename?: Maybe<Scalars['String']['output']>;
  chargeDetails?: Maybe<History_ChargeDetails>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  chargepoint?: Maybe<History_ChargePoint>;
  duration?: Maybe<History_ChargeDuration>;
  eventTime?: Maybe<Scalars['String']['output']>;
  homeCountry?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentStatus?: Maybe<Scalars['String']['output']>;
  receiptId?: Maybe<Scalars['String']['output']>;
  referenceChargeSessionId?: Maybe<Scalars['String']['output']>;
  sessionId?: Maybe<Scalars['String']['output']>;
  timestamp?: Maybe<History_TimeStamp>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  userType?: Maybe<Scalars['String']['output']>;
};

export type History_HistoryRecordInput = {
  chargeDetails?: InputMaybe<History_ChargeDetailsInput>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  chargepoint?: InputMaybe<History_ChargepointInput>;
  dateEnd?: InputMaybe<Scalars['String']['input']>;
  dateStart?: InputMaybe<Scalars['String']['input']>;
  eventTime?: InputMaybe<Scalars['String']['input']>;
  payback?: InputMaybe<Scalars['String']['input']>;
  sessionId?: InputMaybe<Scalars['String']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  transactionNumber?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type History_HistoryRecordsResponse = {
  __typename?: 'History_HistoryRecordsResponse';
  count?: Maybe<Scalars['Int']['output']>;
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  lastKey?: Maybe<History_LastEvaluatedKey>;
  page?: Maybe<Scalars['Int']['output']>;
  results?: Maybe<Array<Maybe<History_HistoryRecord>>>;
};

export type History_LastEvaluatedKey = {
  __typename?: 'History_LastEvaluatedKey';
  charge_session_id?: Maybe<Scalars['String']['output']>;
  date_end?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type History_LastEvaluatedKeyInput = {
  charge_session_id?: InputMaybe<Scalars['String']['input']>;
  date_end?: InputMaybe<Scalars['String']['input']>;
  date_start?: InputMaybe<Scalars['String']['input']>;
  user_id?: InputMaybe<Scalars['String']['input']>;
};

export type History_OfferCode = {
  __typename?: 'History_OfferCode';
  offerCode: Scalars['String']['output'];
  offerValue: Scalars['Float']['output'];
};

export type History_OfferCodeInput = {
  offerCode: Scalars['String']['input'];
  offerValue: Scalars['Float']['input'];
};

export type History_OverstayCost = {
  __typename?: 'History_OverstayCost';
  duration?: Maybe<Scalars['String']['output']>;
  fineGross?: Maybe<Scalars['String']['output']>;
  fineNet?: Maybe<Scalars['String']['output']>;
  fineTax?: Maybe<Scalars['String']['output']>;
  fineTaxPercentage?: Maybe<Scalars['String']['output']>;
  fineUnitCost?: Maybe<Scalars['String']['output']>;
};

export type History_OverstayCostInput = {
  duration?: InputMaybe<Scalars['String']['input']>;
  fineGross?: InputMaybe<Scalars['Float']['input']>;
  fineNet?: InputMaybe<Scalars['Float']['input']>;
  fineTax?: InputMaybe<Scalars['Float']['input']>;
  fineTaxPercentage?: InputMaybe<Scalars['Float']['input']>;
  fineUnitCost?: InputMaybe<Scalars['Float']['input']>;
};

export type History_ProviderChargeIds = {
  __typename?: 'History_ProviderChargeIds';
  type?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type History_TimeStamp = {
  __typename?: 'History_TimeStamp';
  start?: Maybe<Scalars['String']['output']>;
  stop?: Maybe<Scalars['String']['output']>;
};

export type History_UpdateHistoryResponse = {
  __typename?: 'History_UpdateHistoryResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createHistoryRecord?: Maybe<History_CreateHistoryResponse>;
  deleteUserChargeEvents?: Maybe<DeleteUserChargeEventsResponse>;
  updateHistoryRecord?: Maybe<History_UpdateHistoryResponse>;
};

export type MutationCreateHistoryRecordArgs = {
  cdrType?: InputMaybe<Scalars['String']['input']>;
  cdr_filename?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  historyRecord: History_HistoryRecordInput;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentStatus: PaymentStatus;
  referenceChargeSessionId?: InputMaybe<Scalars['String']['input']>;
  sessionType?: InputMaybe<SessionType>;
  userId?: InputMaybe<Scalars['String']['input']>;
  userType?: InputMaybe<Scalars['String']['input']>;
};

export type MutationDeleteUserChargeEventsArgs = {
  privateId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type MutationUpdateHistoryRecordArgs = {
  chargeSessionId: Scalars['String']['input'];
  discount?: InputMaybe<Scalars['Float']['input']>;
  offerCodes?: InputMaybe<Array<History_OfferCodeInput>>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentStatus?: InputMaybe<Scalars['String']['input']>;
  referenceChargeSessionId?: InputMaybe<Scalars['String']['input']>;
};

export enum PaymentStatus {
  Captured = 'Captured',
  Contactless = 'Contactless',
  Outstanding = 'Outstanding',
  Preauthorised = 'Preauthorised',
  Processing = 'Processing',
}

export type Query = {
  __typename?: 'Query';
  getHistoryRecord?: Maybe<History_HistoryRecord>;
  getHistoryRecords?: Maybe<History_HistoryRecordsResponse>;
  getHistoryRecordsByIds?: Maybe<Array<Maybe<History_HistoryRecord>>>;
  getLatestHistoryRecord?: Maybe<History_HistoryRecord>;
  searchInvoices?: Maybe<Array<Maybe<History_HistoryRecord>>>;
};

export type QueryGetHistoryRecordArgs = {
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  sessionId?: InputMaybe<Scalars['String']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type QueryGetHistoryRecordsArgs = {
  appCountry: Scalars['String']['input'];
  endDate?: InputMaybe<Scalars['String']['input']>;
  lastKey?: InputMaybe<History_LastEvaluatedKeyInput>;
  page?: InputMaybe<Scalars['Int']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type QueryGetHistoryRecordsByIdsArgs = {
  chargeSessionIds: Array<Scalars['String']['input']>;
};

export type QueryGetLatestHistoryRecordArgs = {
  appCountry: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type QuerySearchInvoicesArgs = {
  cardDigits?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  endDate: Scalars['String']['input'];
  grossAmount: Scalars['String']['input'];
};

export enum SessionType {
  Contactless = 'Contactless',
  Mobile = 'Mobile',
  RFID = 'RFID',
  Webshop = 'Webshop',
}

export type GetHistoryRecordQueryVariables = Exact<{
  userId?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId: Scalars['String']['input'];
}>;

export type GetHistoryRecordQuery = {
  __typename?: 'Query';
  getHistoryRecord?: {
    __typename?: 'History_HistoryRecord';
    userId?: string | null;
    paymentStatus?: string | null;
    homeCountry?: string | null;
    chargeDetails?: {
      __typename?: 'History_ChargeDetails';
      tagId?: string | null;
      totalGross?: string | null;
      currency?: string | null;
      payback?: string | null;
      isStandardInvoice?: boolean | null;
    } | null;
  } | null;
};

export type UpdateHistoryRecordMutationVariables = Exact<{
  chargeSessionId: Scalars['String']['input'];
  referenceChargeSessionId?: InputMaybe<Scalars['String']['input']>;
  paymentStatus?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  discount?: InputMaybe<Scalars['Float']['input']>;
  offerCodes?: InputMaybe<
    Array<History_OfferCodeInput> | History_OfferCodeInput
  >;
}>;

export type UpdateHistoryRecordMutation = {
  __typename?: 'Mutation';
  updateHistoryRecord?: {
    __typename?: 'History_UpdateHistoryResponse';
    status?: string | null;
    message?: string | null;
  } | null;
};

export const GetHistoryRecordDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: { kind: 'Name', value: 'getHistoryRecord' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'userId' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'chargeSessionId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'getHistoryRecord' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'userId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'userId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'chargeSessionId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'chargeSessionId' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                { kind: 'Field', name: { kind: 'Name', value: 'userId' } },
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'chargeDetails' },
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      { kind: 'Field', name: { kind: 'Name', value: 'tagId' } },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'totalGross' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'currency' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'payback' },
                      },
                      {
                        kind: 'Field',
                        name: { kind: 'Name', value: 'isStandardInvoice' },
                      },
                    ],
                  },
                },
                {
                  kind: 'Field',
                  name: { kind: 'Name', value: 'paymentStatus' },
                },
                { kind: 'Field', name: { kind: 'Name', value: 'homeCountry' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetHistoryRecordQuery,
  GetHistoryRecordQueryVariables
>;
export const UpdateHistoryRecordDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: { kind: 'Name', value: 'updateHistoryRecord' },
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'chargeSessionId' },
          },
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'NamedType',
              name: { kind: 'Name', value: 'String' },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'referenceChargeSessionId' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'paymentStatus' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'paymentId' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'discount' },
          },
          type: { kind: 'NamedType', name: { kind: 'Name', value: 'Float' } },
        },
        {
          kind: 'VariableDefinition',
          variable: {
            kind: 'Variable',
            name: { kind: 'Name', value: 'offerCodes' },
          },
          type: {
            kind: 'ListType',
            type: {
              kind: 'NonNullType',
              type: {
                kind: 'NamedType',
                name: { kind: 'Name', value: 'History_OfferCodeInput' },
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: { kind: 'Name', value: 'updateHistoryRecord' },
            arguments: [
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'chargeSessionId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'chargeSessionId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'referenceChargeSessionId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'referenceChargeSessionId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'paymentStatus' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'paymentStatus' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'paymentId' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'paymentId' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'discount' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'discount' },
                },
              },
              {
                kind: 'Argument',
                name: { kind: 'Name', value: 'offerCodes' },
                value: {
                  kind: 'Variable',
                  name: { kind: 'Name', value: 'offerCodes' },
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                { kind: 'Field', name: { kind: 'Name', value: 'status' } },
                { kind: 'Field', name: { kind: 'Name', value: 'message' } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateHistoryRecordMutation,
  UpdateHistoryRecordMutationVariables
>;
